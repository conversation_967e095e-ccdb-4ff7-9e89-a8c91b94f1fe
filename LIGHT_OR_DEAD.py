# -*- coding: utf-8 -*-
import pygame
from pygame.locals import *
import sys
import random
import math
import numpy as np
import time
import os # Pro práci se soubory (high score)

# --- OpenGL ---
from OpenGL.GL import *
from OpenGL.GLU import * # Pro gluErrorString
from OpenGL.GL import shaders
from ctypes import c_float, c_void_p, sizeof # Pro glVertexAttribPointer

# --- Inicializace Pygame (s OpenGL) ---
pygame.init()
pygame.mixer.init()  # Inicializace zvukového systému

# --- Konstanty ---
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
WINDOW_TITLE = "Light or Dead (Enhanced)" # Updated title
FPS = 60
HIGH_SCORE_FILE = "light_or_dead_highscore.txt"

# --- Performance Settings ---
ENABLE_SPATIAL_HASH = True  # Spatial partitioning for collision detection
OBJECT_POOLING = True       # Reuse objects instead of creating new ones
RENDER_OPTIMIZATION = True  # Optimize rendering
QUALITY_LEVEL = 1           # 0=Low, 1=Medium, 2=High
VSYNC_ENABLED = True        # Zapnutí vertikální synchronizace pro plynulejší vykreslování

# --- Trap Settings ---
TRAP_SPIKE_DAMAGE = 15      # Poškození od bodců
TRAP_SPIKE_SIZE = 24        # Velikost bodců
TRAP_POISON_DAMAGE = 2      # Poškození za sekundu od jedu
TRAP_POISON_DURATION = 5.0  # Trvání jedu v sekundách
TRAP_POISON_SIZE = 30       # Velikost jedové pasti
TRAP_SLOW_FACTOR = 0.5      # Zpomalení hráče (násobitel)
TRAP_SLOW_DURATION = 3.0    # Trvání zpomalení v sekundách
TRAP_SLOW_SIZE = 28         # Velikost zpomalovací pasti
TRAP_EXPLOSION_DAMAGE = 25  # Poškození od výbuchu
TRAP_EXPLOSION_SIZE = 32    # Velikost výbušné pasti
TRAP_TELEPORT_SIZE = 30     # Velikost teleportační pasti
TRAP_SPAWN_CHANCE = 0.2     # Šance na spawn pasti při vytvoření vlny (0-1)

# --- Konstanty pro upgrady ---
MAX_FIRE_RATE_UPGRADES = 3  # Maximální počet upgradů fire rate (sníženo)
MAX_MOVE_SPEED_UPGRADES = 2  # Maximální počet upgradů pohybu (sníženo)
MAX_BULLET_DAMAGE_UPGRADES = 4  # Maximální počet upgradů poškození (sníženo)
MAX_LIGHT_RADIUS_UPGRADES = 4  # Maximální počet upgradů světla

# Barvy
WHITE = (255, 255, 255); BLACK = (0, 0, 0); RED = (255, 0, 0)
GREEN = (0, 255, 0); BLUE = (0, 0, 255); YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0); PURPLE = (128, 0, 128); CYAN = (0, 255, 255)
MAGENTA = (255, 0, 255); GREY = (128, 128, 128); DARK_GREY = (40, 40, 40)
LIGHT_BLUE = (173, 216, 230); TRANSPARENT = (0, 0, 0, 0)
BOSS_COLOR = MAGENTA # Barva bosse
BOSS_PROJECTILE_COLOR = (255, 0, 255) # Fialová pro střely bosse
MINION_COLOR = (200, 0, 0) # Tmavší červená pro miniony

# Barvy pastí
TRAP_SPIKE_COLOR = (180, 180, 180)  # Šedá pro bodce
TRAP_POISON_COLOR = (0, 180, 0)     # Tmavě zelená pro jed
TRAP_SLOW_COLOR = (100, 100, 255)   # Světle modrá pro zpomalení
TRAP_EXPLOSION_COLOR = (255, 100, 0)  # Oranžová pro výbuch
TRAP_TELEPORT_COLOR = (150, 0, 255)   # Fialová pro teleportaci

# Typy pastí
TRAP_TYPE_SPIKE = 0   # Bodce - okamžité poškození
TRAP_TYPE_POISON = 1  # Jed - poškození za čas
TRAP_TYPE_SLOW = 2    # Zpomalení - snížení rychlosti
TRAP_TYPE_EXPLOSION = 3  # Nová past - výbuch
TRAP_TYPE_TELEPORT = 4   # Nová past - teleportace

# Herní stavy
STATE_SPLASH = 0; STATE_MAIN_MENU = 1; STATE_PLAYING = 2; STATE_SHOP = 3; STATE_GAME_OVER = 4; STATE_SETTINGS = 5

# Hráč
PLAYER_SIZE = 30; PLAYER_BASE_SPEED = 120  # Sníženo z 200 na reálnější rychlost
PLAYER_START_X = SCREEN_WIDTH // 2; PLAYER_START_Y = SCREEN_HEIGHT // 2
PLAYER_MAX_HEALTH = 100

# Střelba Hráče
BULLET_SIZE = 5; BASE_BULLET_SPEED = 300; BULLET_COLOR = YELLOW  # Sníženo z 400
BASE_BULLET_COOLDOWN = 0.4; BASE_BULLET_DAMAGE = 2  # Zvýšen cooldown a damage
bullets = []; last_shot_time = 0

# Světlo
INITIAL_LIGHT_RADIUS = 180.0; MAX_LIGHT_RADIUS = 450.0
MIN_LIGHT_RADIUS = 35.0; LIGHT_DEATH_THRESHOLD = -1.0
LIGHT_FLICKER_AMOUNT = 1.5; LIGHT_SHRINK_RATE = 3.0  # Sníženo blikání a zpomaleno zmenšování
LIGHT_GAIN_PER_KILL = 8.0; LIGHT_GROW_INTERP_FACTOR = 16.0
LIGHT_COLOR = (255, 220, 150); AMBIENT_LIGHT = (0.03, 0.03, 0.08)  # Teplejší barva světla (jako svíčka)

# Nepřátelé (Běžní)
ENEMY_BASE_SIZE = 20
ENEMY_BASE_SPEED = 80.0  # Zvýšená základní rychlost
ENEMY_BASE_HEALTH = 5    # Zvýšené základní HP
ENEMY_BASE_DAMAGE = 15   # Zvýšené základní poškození
ENEMY_BASE_SCORE = 10
enemies = []
ENEMY_COLOR = RED

# Střelba nepřátel
ENEMY_SHOOTING_START_WAVE = 3  # Od které vlny začnou nepřátelé střílet
ENEMY_SHOOTING_CHANCE = 0.2    # Šance, že nepřítel bude moci střílet (20%)
ENEMY_SHOOTING_COOLDOWN = 2.0  # Doba mezi střelami nepřítele v sekundách
ENEMY_BULLET_SPEED = 150       # Rychlost střel nepřátel
ENEMY_BULLET_DAMAGE = 5        # Poškození střel nepřátel
ENEMY_BULLET_SIZE = 8          # Velikost střel nepřátel
ENEMY_BULLET_COLOR = ORANGE    # Barva střel nepřátel
enemy_bullets = []             # Seznam pro střely nepřátel

# --- Boss Konstanty & Mechaniky ---
BOSS_WAVE_INTERVAL = 5
BOSS_SIZE = 50
# Škálování HP: Základní * Multiplikátor + Bonus za Skóre
BOSS_HEALTH_MULTIPLIER = 8     # Snížený násobek HP pro lepší gameplay
BOSS_SCORE_HP_FACTOR = 0.1     # Kolik HP navíc za každých 100 bodů skóre hráče (při spawnu)
# Ostatní škálování (méně závislé na skóre)
BOSS_SPEED_MULTIPLIER = 0.8    # Snížená rychlost bosse pro lepší gameplay
BOSS_DAMAGE_MULTIPLIER = 2.5   # Zvýšené poškození
BOSS_SCORE_MULTIPLIER = 10     # Více bodů za zabití
BOSS_LIGHT_REWARD = 100        # Odměna světla za zabití bosse

# Mechaniky Cooldowny (v sekundách)
BOSS_ACTION_COOLDOWN_MIN = 2.5
BOSS_ACTION_COOLDOWN_MAX = 4.0
BOSS_DASH_SPEED = 450
BOSS_DASH_DURATION = 0.4
BOSS_PROJECTILE_SPEED = 280
BOSS_PROJECTILE_DAMAGE = 20
BOSS_PROJECTILE_SIZE = 10
BOSS_SUMMON_COUNT_MIN = 2
BOSS_SUMMON_COUNT_MAX = 4
BOSS_SUMMON_HEALTH_FACTOR = 0.5 # Minioni mají % HP běžných nepřátel dané vlny

# Nové typy bosse
BOSS_TYPE_NORMAL = 0    # Základní boss
BOSS_TYPE_SPEED = 1     # Rychlý boss s menším HP
BOSS_TYPE_TANK = 2      # Pomalý boss s vysokým HP
BOSS_TYPE_SUMMONER = 3  # Boss zaměřený na přivolávání minionů
BOSS_TYPE_SHOOTER = 4   # Boss zaměřený na střelbu

# Konstanty pro různé typy bosse
BOSS_TYPES = {
    BOSS_TYPE_NORMAL: {
        'health_multiplier': 1.0,
        'speed_multiplier': 1.0,
        'damage_multiplier': 1.0,
        'color': MAGENTA
    },
    BOSS_TYPE_SPEED: {
        'health_multiplier': 0.7,
        'speed_multiplier': 1.5,
        'damage_multiplier': 0.8,
        'color': CYAN
    },
    BOSS_TYPE_TANK: {
        'health_multiplier': 2.0,
        'speed_multiplier': 0.7,
        'damage_multiplier': 1.2,
        'color': ORANGE
    },
    BOSS_TYPE_SUMMONER: {
        'health_multiplier': 0.8,
        'speed_multiplier': 0.9,
        'damage_multiplier': 0.7,
        'summon_chance': 0.4,
        'color': PURPLE
    },
    BOSS_TYPE_SHOOTER: {
        'health_multiplier': 0.9,
        'speed_multiplier': 1.1,
        'damage_multiplier': 1.3,
        'shoot_chance': 0.4,
        'color': RED
    }
}

# Seznam pro projektily bosse
boss_bullets = []

# Wave System
current_wave = 0; enemies_to_spawn_in_wave = 0; enemies_alive_in_wave = 0
wave_transition_timer = 0.0; WAVE_TRANSITION_DELAY = 4.0
WAVE_MESSAGE_DURATION = 2.5; wave_message_timer = 0.0; wave_message_text = ""

# Obchod (Grid Layout)
SHOP_COLS = 3; SHOP_ITEM_WIDTH = 200; SHOP_ITEM_HEIGHT = 80
SHOP_PADDING_X = 20; SHOP_PADDING_Y = 20
SHOP_GRID_START_X = (SCREEN_WIDTH - (SHOP_COLS * SHOP_ITEM_WIDTH + (SHOP_COLS - 1) * SHOP_PADDING_X)) // 2
SHOP_GRID_START_Y = 180

# --- Funkce pro high score (beze změny) ---
def load_highscore():
    if os.path.exists(HIGH_SCORE_FILE):
        try:
            with open(HIGH_SCORE_FILE, 'r') as f: return int(f.read().strip())
        except (ValueError, IOError): print(f"Chyba čtení high score: {HIGH_SCORE_FILE}"); return 0
    return 0
def save_highscore(score):
    try:
        with open(HIGH_SCORE_FILE, 'w') as f: f.write(str(score))
    except IOError: print(f"Chyba zápisu high score: {HIGH_SCORE_FILE}")

# --- Shop Items Data (Rozšířeno o zbraně) ---
# Funkce pro nákup zbraní
def buy_weapon(item_id):
    """Nakupování v obchodě"""
    for item in shop_items:
        if item['id'] == item_id:
            # Kontrola, zda má hráč dostatek bodů
            if player_stats['score'] >= item['cost']:
                # Kontrola maximálního počtu upgradů
                if 'max_upgrades' in item:
                    # Získání aktuálního počtu upgradů
                    current_upgrades = 0
                    if item_id == 'firerate':
                        current_upgrades = int((player_stats['fire_rate_multiplier'] - 1.0) / 0.15)
                    elif item_id == 'move_speed':
                        current_upgrades = int((player_stats['speed_multiplier'] - 1.0) / 0.15)
                    elif item_id == 'bullet_dmg':
                        current_upgrades = player_stats['bullet_damage'] - 2  # Upraveno pro nový BASE_BULLET_DAMAGE
                    elif item_id == 'light':
                        current_upgrades = int(player_stats['light_to_add'] / 50)
                    
                    # Kontrola, zda nebyl překročen maximální počet upgradů
                    if current_upgrades >= item['max_upgrades']:
                        sound_manager.play_sound('error')
                        return False
                
                # Provedení nákupu
                player_stats['score'] -= item['cost']
                item['effect'](player_stats)
                sound_manager.play_sound('shop_buy')
                return True
            else:
                sound_manager.play_sound('error')
                return False
    return False

def buy_shotgun(stats):
    global weapons
    weapons[WEAPON_SHOTGUN]['owned'] = True
    print("Shotgun purchased!")

def buy_machinegun(stats):
    global weapons
    weapons[WEAPON_MACHINEGUN]['owned'] = True
    print("Machine Gun purchased!")

shop_items = [
    # Vylepšení
    {"id": "health", "name": "Health Pack", "cost": 50, "desc": f"+{40} HP", "effect": lambda stats: stats.update({'health': min(stats['max_health'], stats['health'] + 40)})},
    {"id": "light", "name": "Light Radius", "cost": 75, "desc": f"+{50} Radius (0/{MAX_LIGHT_RADIUS_UPGRADES})", "effect": lambda stats: stats.update({'light_to_add': stats['light_to_add'] + 50}), "max_upgrades": MAX_LIGHT_RADIUS_UPGRADES},
    {"id": "firerate", "name": "Fire Rate", "cost": 150, "desc": "+15% Speed (0/{MAX_FIRE_RATE_UPGRADES})", "effect": lambda stats: stats.update({'fire_rate_multiplier': stats['fire_rate_multiplier'] * 1.15}), "max_upgrades": MAX_FIRE_RATE_UPGRADES},
    {"id": "bullet_dmg", "name": "Bullet Damage", "cost": 120, "desc": "+1 Damage (0/{MAX_BULLET_DAMAGE_UPGRADES})", "effect": lambda stats: stats.update({'bullet_damage': stats['bullet_damage'] + 1}), "max_upgrades": MAX_BULLET_DAMAGE_UPGRADES},
    {"id": "move_speed", "name": "Move Speed", "cost": 200, "desc": "+15% Speed (0/{MAX_MOVE_SPEED_UPGRADES})", "effect": lambda stats: stats.update({'speed_multiplier': stats['speed_multiplier'] * 1.15}), "max_upgrades": MAX_MOVE_SPEED_UPGRADES},
    {"id": "max_health", "name": "Max Health", "cost": 150, "desc": "+20 Max HP", "effect": lambda stats: (stats.update({'max_health': stats['max_health'] + 20}), stats.update({'health': stats['health'] + 20}))},

    # Zbraně
    {"id": "shotgun", "name": "Shotgun", "cost": 300, "desc": "5 bullets in spread", "effect": buy_shotgun},
    {"id": "machinegun", "name": "Machine Gun", "cost": 500, "desc": "Rapid fire", "effect": buy_machinegun}
]

# --- Fonty (beze změny) ---
try:
    default_font = pygame.font.Font(None, 36); small_font = pygame.font.Font(None, 24)
    smaller_font = pygame.font.Font(None, 18); large_font = pygame.font.Font(None, 74)
    wave_font = pygame.font.Font(None, 50)
except Exception as e:
    print(f"Chyba fontu: {e}"); default_font = pygame.font.SysFont(pygame.font.get_default_font(), 36)
    small_font = pygame.font.SysFont(pygame.font.get_default_font(), 24); smaller_font = pygame.font.SysFont(pygame.font.get_default_font(), 18)
    large_font = pygame.font.SysFont(pygame.font.get_default_font(), 74); wave_font = pygame.font.SysFont(pygame.font.get_default_font(), 50)

# --- OpenGL Error Checking Helper (beze změny) ---
def check_gl_error(operation_name=""):
    error = glGetError()
    if error != GL_NO_ERROR:
        try: err_string = gluErrorString(error)
        except NameError: err_string = f"Error code {error}"
        except Exception as e: err_string = f"Error code {error}, exception: {e}"
        print(f"OpenGL Error after {operation_name}: {err_string}")
    return error == GL_NO_ERROR

# --- Nastavení okna (OpenGL) (Upraveno pro VSYNC) ---
try:
    # Nastavení VSYNC pro plynulejší vykreslování
    if VSYNC_ENABLED:
        pygame.display.gl_set_attribute(pygame.GL_SWAP_CONTROL, 1)  # Zapnutí VSYNC

    screen_flags = DOUBLEBUF | OPENGL
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT), screen_flags)
    pygame.display.set_caption(WINDOW_TITLE)
    check_gl_error("pygame.display.set_mode")
except pygame.error as e: print(f"Chyba okna: {e}"); pygame.quit(); sys.exit()

clock = pygame.time.Clock()

# --- Shader Kód (beze změny) ---
VERTEX_SHADER = """#version 330 core
layout (location = 0) in vec2 aPos; layout (location = 1) in vec2 aTexCoord;
out vec2 TexCoord; void main() { gl_Position = vec4(aPos.x, aPos.y, 0.0, 1.0); TexCoord = aTexCoord; }"""
GAME_FRAGMENT_SHADER = """#version 330 core
out vec4 FragColor;
in vec2 TexCoord;
uniform sampler2D gameTexture;
uniform vec2 lightPos;
uniform float lightRadius;
uniform vec3 lightColor;
uniform vec2 screenResolution;
uniform vec3 ambientColor;
uniform bool applyLighting;
uniform float time;

// Improved Perlin noise for more natural light flicker
float hash(vec2 p) { 
    return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453); 
}

float noise(vec2 p) {
    vec2 i = floor(p);
    vec2 f = fract(p);
    
    // Improved interpolation
    vec2 u = f * f * (3.0 - 2.0 * f);
    
    float a = hash(i);
    float b = hash(i + vec2(1.0, 0.0));
    float c = hash(i + vec2(0.0, 1.0));
    float d = hash(i + vec2(1.0, 1.0));
    
    return mix(mix(a, b, u.x), mix(c, d, u.x), u.y);
}

// FBM (Fractal Brownian Motion) for more natural light variation
float fbm(vec2 p) {
    float v = 0.0;
    float a = 0.5;
    float f = 1.0;
    for(int i = 0; i < 4; i++) {
        v += a * noise(p * f);
        f *= 2.0;
        a *= 0.5;
    }
    return v;
}

void main() {
    vec4 textureColor = texture(gameTexture, TexCoord);
    if (!applyLighting || textureColor.a == 0.0) {
        FragColor = textureColor;
        return;
    }

    vec2 pixelCoord = TexCoord * screenResolution;
    float distance = length(pixelCoord - lightPos);

    // Dynamic light radius with natural flicker
    float flicker = fbm(vec2(time * 0.5, time * 0.7)) * 0.15 + 0.85;
    float innerRadius = lightRadius * 0.4 * flicker;
    float outerRadius = lightRadius * flicker;

    // Improved light falloff with soft edges
    float lightIntensity = 1.0;
    if (distance > innerRadius) {
        float falloff = smoothstep(innerRadius, outerRadius, distance);
        lightIntensity = 1.0 - pow(falloff, 1.5);
    }

    // Dynamic color variation for more atmospheric lighting
    vec3 warmGlow = vec3(1.0, 0.9, 0.7) * lightColor;
    vec3 coolGlow = vec3(0.7, 0.8, 1.0) * lightColor;
    vec3 dynamicColor = mix(warmGlow, coolGlow, fbm(vec2(time * 0.2, time * 0.3)));

    // Final color with improved ambient lighting
    vec3 finalColor = mix(textureColor.rgb * ambientColor, textureColor.rgb * dynamicColor, lightIntensity);
    
    // Add subtle color variation based on distance
    float colorVariation = fbm(vec2(distance * 0.01, time * 0.1)) * 0.1;
    finalColor += vec3(colorVariation);

    FragColor = vec4(finalColor, textureColor.a);
}"""
MENU_FRAGMENT_SHADER = """#version 330 core
out vec4 FragColor; uniform vec2 screenResolution; uniform float time;
float random(vec2 st){return fract(sin(dot(st.xy+time*0.001,vec2(12.9898,78.233)))*43758.5453123);}
float noise(vec2 st){vec2 i=floor(st);vec2 f=fract(st);vec2 t=vec2(time*0.02,time*0.01);float a=random(i+t);float b=random(i+vec2(1.0,0.0)+t);float c=random(i+vec2(0.0,1.0)+t);float d=random(i+vec2(1.0,1.0)+t);vec2 u=f*f*(3.0-2.0*f);return mix(mix(a,b,u.x),mix(c,d,u.x),u.y);}
#define OCTAVES 6
float fbm(vec2 st){float v=0.0;float a=0.5;float fr=1.0;for(int i=0;i<OCTAVES;i++){v+=a*noise(st*fr);fr*=2.0;a*=0.5;}return v;}
float stars(vec2 uv,float ds,float sz,float i){vec2 suv=uv+vec2(time*0.001/ds,time*0.0005/ds);float dn=noise(suv*ds);float th=1.0-(sz*50.0);float sp=smoothstep(th-0.002,th+0.002,dn);float fs=fract(time*0.3+dot(uv,vec2(10.0,5.0)));float fl=pow(random(vec2(fs)),25.0);sp*=smoothstep(0.2,0.8,fl+0.4);return sp*i;}
void main(){vec2 uv=gl_FragCoord.xy/screenResolution.xy;vec3 fc=vec3(0.0,0.0,0.02);vec2 nuv1=uv*1.5+vec2(time*0.025,time*0.01);vec2 nuv2=uv*0.8+vec2(-time*0.015,time*0.02);vec2 nuv3=uv*3.5-vec2(time*0.04,-time*0.015);float np1=fbm(nuv1);float np2=fbm(nuv2+3.0);float np3=fbm(nuv3+7.0);vec3 nc1=vec3(0.1,0.08,0.45);vec3 nc2=vec3(0.45,0.1,0.35);vec3 nc3=vec3(0.08,0.25,0.3);fc=mix(fc,nc1,smoothstep(0.4,0.6,np1)*0.65);fc=mix(fc,nc2,smoothstep(0.45,0.65,np2)*0.55);fc=mix(fc,nc3,smoothstep(0.5,0.6,np3)*0.4);float sl1=stars(uv,100.0,0.008,0.9);float sl2=stars(uv,50.0,0.015,0.7);float sl3=stars(uv,25.0,0.025,0.5);vec3 sc=vec3(0.95,0.95,1.0);float ts=clamp(sl1+sl2+sl3,0.0,1.0);fc+=sc*ts;fc=clamp(fc,0.0,1.0);float vd=length(uv-vec2(0.5));float vi=0.5;fc*=smoothstep(0.8,0.3,vd*(1.0+vi));FragColor=vec4(fc,1.0); }"""

# --- Funkce pro shadery a OpenGL (beze změny) ---
def compile_shader(source, shader_type):
    try:
        shader = glCreateShader(shader_type); glShaderSource(shader, source); glCompileShader(shader)
        if not glGetShaderiv(shader, GL_COMPILE_STATUS): error = glGetShaderInfoLog(shader).decode(); glDeleteShader(shader); shader_name = 'Vertex' if shader_type == GL_VERTEX_SHADER else 'Fragment'; print(f"Chyba kompilace {shader_name}:\n{error}"); return None
        return shader
    except Exception as e: shader_name = 'Vertex' if shader_type == GL_VERTEX_SHADER else 'Fragment'; print(f"Výjimka kompilace {shader_name}: {e}"); return None
def create_shader_program(vertex_source, fragment_source, program_name="Shader"):
    program = None; vertex_shader = None; fragment_shader = None
    try:
        vertex_shader = compile_shader(vertex_source, GL_VERTEX_SHADER); fragment_shader = compile_shader(fragment_source, GL_FRAGMENT_SHADER)
        if not vertex_shader or not fragment_shader: raise RuntimeError(f"Chyba kompilace shaderu pro {program_name}.")
        program = glCreateProgram(); glAttachShader(program, vertex_shader); glAttachShader(program, fragment_shader); glLinkProgram(program)
        if not glGetProgramiv(program, GL_LINK_STATUS): error = glGetProgramInfoLog(program).decode(); glDeleteProgram(program); program = None; raise RuntimeError(f"Chyba linkování {program_name}: {error}")
        glDetachShader(program, vertex_shader); glDetachShader(program, fragment_shader); print(f"{program_name} (ID: {program}) vytvořen."); return program
    except Exception as e: print(f"Chyba vytváření {program_name}: {e}"); return None
    finally:
        if vertex_shader: glDeleteShader(vertex_shader)
        if fragment_shader: glDeleteShader(fragment_shader)

# --- OpenGL příprava (VAO/VBO/EBO) (beze změny) ---
shader_program_ok = True; game_shader_program = None; menu_shader_program = None
VAO, VBO, EBO = None, None, None; game_texture = None
loc_game_lightPos, loc_game_lightRadius, loc_game_lightColor = -1, -1, -1
loc_game_screenResolution, loc_game_ambientColor, loc_game_gameTexture = -1, -1, -1
loc_game_applyLighting, loc_game_time = -1, -1
loc_menu_screenResolution, loc_menu_time = -1, -1
try:
    print("--- OpenGL Init ---"); game_shader_program = create_shader_program(VERTEX_SHADER, GAME_FRAGMENT_SHADER, "Game"); menu_shader_program = create_shader_program(VERTEX_SHADER, MENU_FRAGMENT_SHADER, "Menu")
    if not game_shader_program or not menu_shader_program: shader_program_ok = False; raise RuntimeError("Shader creation failed.")
    quad_vertices = np.array([-1.0, 1.0, 0.0, 1.0, -1.0, -1.0, 0.0, 0.0, 1.0, -1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0], dtype=np.float32)
    quad_indices = np.array([0, 1, 2, 0, 2, 3], dtype=np.uint32)
    VAO = glGenVertexArrays(1); glBindVertexArray(VAO); VBO = glGenBuffers(1); glBindBuffer(GL_ARRAY_BUFFER, VBO); glBufferData(GL_ARRAY_BUFFER, quad_vertices.nbytes, quad_vertices, GL_STATIC_DRAW)
    EBO = glGenBuffers(1); glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, EBO); glBufferData(GL_ELEMENT_ARRAY_BUFFER, quad_indices.nbytes, quad_indices, GL_STATIC_DRAW)
    glVertexAttribPointer(0, 2, GL_FLOAT, GL_FALSE, 4*sizeof(GLfloat), c_void_p(0)); glEnableVertexAttribArray(0); glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 4*sizeof(GLfloat), c_void_p(2*sizeof(GLfloat))); glEnableVertexAttribArray(1)
    glBindBuffer(GL_ARRAY_BUFFER, 0); glBindVertexArray(0); check_gl_error("VAO/VBO/EBO setup")
    game_texture = glGenTextures(1); glBindTexture(GL_TEXTURE_2D, game_texture); glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE); glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE)
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR); glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR); glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, SCREEN_WIDTH, SCREEN_HEIGHT, 0, GL_RGBA, GL_UNSIGNED_BYTE, None); glBindTexture(GL_TEXTURE_2D, 0); check_gl_error("Texture setup")
    glUseProgram(game_shader_program)
    loc_game_lightPos=glGetUniformLocation(game_shader_program,"lightPos")
    loc_game_lightRadius=glGetUniformLocation(game_shader_program,"lightRadius")
    loc_game_lightColor=glGetUniformLocation(game_shader_program,"lightColor")
    loc_game_screenResolution=glGetUniformLocation(game_shader_program,"screenResolution")
    loc_game_ambientColor=glGetUniformLocation(game_shader_program,"ambientColor")
    loc_game_gameTexture=glGetUniformLocation(game_shader_program,"gameTexture")
    loc_game_applyLighting=glGetUniformLocation(game_shader_program,"applyLighting")
    loc_game_time=glGetUniformLocation(game_shader_program,"time")
    glUniform1i(loc_game_gameTexture,0)
    glUseProgram(0)
    check_gl_error("Game uniforms")
    glUseProgram(menu_shader_program); loc_menu_screenResolution=glGetUniformLocation(menu_shader_program,"screenResolution"); loc_menu_time=glGetUniformLocation(menu_shader_program,"time"); glUseProgram(0); check_gl_error("Menu uniforms")
    if any(loc == -1 for loc in [loc_game_gameTexture,loc_game_applyLighting,loc_menu_screenResolution,loc_menu_time]): print("WARN: Missing uniforms!")
    print("--- OpenGL OK ---")
except Exception as e: print(f"FATAL OpenGL Init Error: {e}"); shader_program_ok = False
if not shader_program_ok: print("OpenGL init failed. Exiting."); pygame.quit(); sys.exit()

# Pomocná plocha pro kreslení
draw_surface = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)

# --- Spatial Hash Grid pro optimalizaci kolizí ---
class SpatialHashGrid:
    def __init__(self, cell_size=50):
        self.cell_size = cell_size
        self.grid = {}

    def clear(self):
        self.grid.clear()

    def get_cell(self, x, y):
        return (int(x // self.cell_size), int(y // self.cell_size))

    def insert(self, obj, obj_id):
        cell = self.get_cell(obj['x'], obj['y'])
        if cell not in self.grid:
            self.grid[cell] = set()
        self.grid[cell].add(obj_id)

    def query_nearby(self, x, y, radius=1):
        results = set()
        center_cell = self.get_cell(x, y)

        # Check center cell and surrounding cells
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                cell = (center_cell[0] + dx, center_cell[1] + dy)
                if cell in self.grid:
                    results.update(self.grid[cell])

        return results

# --- Object Pool pro optimalizaci alokace ---
class ObjectPool:
    def __init__(self, create_func, max_size=100):
        self.pool = []
        self.create_func = create_func
        self.max_size = max_size

    def get(self):
        if self.pool:
            return self.pool.pop()
        return self.create_func()

    def release(self, obj):
        if len(self.pool) < self.max_size:
            self.pool.append(obj)

# --- Splash Screen ---
SPLASH_DURATION = 5.0  # Prodlouženo trvání splash screenu na 5 sekund
splash_start_time = 0.0
splash_alpha = 0  # Pro fade efekt

# --- Nastavení hry ---
settings = {
    'resolution': (SCREEN_WIDTH, SCREEN_HEIGHT),
    'show_fps': True,
    'vsync': VSYNC_ENABLED,
    'quality': QUALITY_LEVEL,
    'volume': 0.7,
    'music_volume': 0.5,
    'difficulty': 1,  # 0=Easy, 1=Normal, 2=Hard
    'keybinds': {
        'move_up': pygame.K_w,
        'move_down': pygame.K_s,
        'move_left': pygame.K_a,
        'move_right': pygame.K_d,
        'weapon_1': pygame.K_1,
        'weapon_2': pygame.K_2,
        'weapon_3': pygame.K_3,
        'shop': pygame.K_p,
    }
}

# --- Typy zbraní ---
WEAPON_PISTOL = 0
WEAPON_SHOTGUN = 1
WEAPON_MACHINEGUN = 2

# Definice zbraní - vybalancované hodnoty
weapons = [
    {
        'name': 'Pistol',
        'damage': 1.0,  # Základní damage
        'fire_rate': 1.0,  # Násobitel základní rychlosti střelby
        'bullet_speed': BASE_BULLET_SPEED,
        'bullet_size': BULLET_SIZE,
        'bullet_count': 1,
        'spread': 0.0,
        'color': YELLOW,
        'cost': 0,  # Základní zbraň
        'owned': True,
        'description': 'Standard pistol - balanced'
    },
    {
        'name': 'Shotgun',
        'damage': 0.7,  # Nižší damage per bullet, ale více bullets
        'fire_rate': 0.6,  # Pomalejší střelba
        'bullet_speed': BASE_BULLET_SPEED * 0.85,
        'bullet_size': BULLET_SIZE * 1.1,
        'bullet_count': 5,  # Sníženo z 7 na 5 pro balance
        'spread': 12.0,     # Mírně snížen rozptyl
        'color': ORANGE,
        'cost': 300,
        'owned': False,
        'description': 'Close range, 5 bullets'
    },
    {
        'name': 'Machine Gun',
        'damage': 0.6,  # Nižší damage per bullet
        'fire_rate': 2.5,  # Sníženo z 4.0 na rozumnější hodnotu
        'bullet_speed': BASE_BULLET_SPEED * 1.05,
        'bullet_size': BULLET_SIZE * 0.9,
        'bullet_count': 1,
        'spread': 5.0,     # Snížen rozptyl pro lepší přesnost
        'color': CYAN,
        'cost': 500,
        'owned': False,
        'description': 'Fast fire, lower damage'
    }
]

# --- Načtení assetů ---
def load_assets():
    assets = {}
    assets_folder = "game_assets"
    
    try:
        # Načtení zvuků
        sound_manager.load_sounds()
        
        # Create assets folder if it doesn't exist
        if not os.path.exists(assets_folder):
            os.makedirs(assets_folder)

        # Player texture
        player_path = os.path.join(assets_folder, "player.png")
        if os.path.exists(player_path):
            assets['player'] = pygame.image.load(player_path).convert_alpha()
        else:
            # Create procedural player texture
            player_surface = pygame.Surface((60, 60), pygame.SRCALPHA)
            # Main body
            pygame.draw.circle(player_surface, (255, 255, 255), (30, 30), 25)
            # Inner glow
            pygame.draw.circle(player_surface, (255, 220, 180), (30, 30), 20)
            # Eyes
            pygame.draw.circle(player_surface, (255, 255, 255), (20, 25), 8)
            pygame.draw.circle(player_surface, (255, 255, 255), (40, 25), 8)
            pygame.draw.circle(player_surface, (0, 0, 0), (20, 25), 4)
            pygame.draw.circle(player_surface, (0, 0, 0), (40, 25), 4)
            assets['player'] = player_surface
            pygame.image.save(player_surface, player_path)

        # Weapon textures
        weapon_types = ["pistol", "shotgun", "machinegun"]
        assets['weapons'] = {}
        for weapon_type in weapon_types:
            weapon_path = os.path.join(assets_folder, f"weapon_{weapon_type}.png")
            if os.path.exists(weapon_path):
                assets['weapons'][weapon_type] = pygame.image.load(weapon_path).convert_alpha()
            else:
                # Create procedural weapon textures
                weapon_surface = pygame.Surface((80, 30), pygame.SRCALPHA)
                if weapon_type == "pistol":
                    # Pistol design
                    pygame.draw.rect(weapon_surface, (100, 100, 100), (0, 10, 40, 15))
                    pygame.draw.rect(weapon_surface, (80, 80, 80), (40, 12, 30, 11))
                elif weapon_type == "shotgun":
                    # Shotgun design
                    pygame.draw.rect(weapon_surface, (120, 120, 120), (0, 5, 50, 20))
                    pygame.draw.rect(weapon_surface, (100, 100, 100), (50, 8, 30, 14))
                else:  # machinegun
                    # Machine gun design
                    pygame.draw.rect(weapon_surface, (90, 90, 90), (0, 8, 60, 14))
                    pygame.draw.rect(weapon_surface, (70, 70, 70), (60, 10, 20, 10))
                assets['weapons'][weapon_type] = weapon_surface
                pygame.image.save(weapon_surface, weapon_path)

        # Enemy textures
        enemy_types = ["basic", "fast", "boss"]
        assets['enemies'] = {}
        for enemy_type in enemy_types:
            enemy_path = os.path.join(assets_folder, f"enemy_{enemy_type}.png")
            if os.path.exists(enemy_path):
                assets['enemies'][enemy_type] = pygame.image.load(enemy_path).convert_alpha()
            else:
                # Create procedural enemy textures
                size = 40 if enemy_type == "basic" else 50 if enemy_type == "fast" else 80
                enemy_surface = pygame.Surface((size, size), pygame.SRCALPHA)
                color = (200, 0, 0) if enemy_type == "basic" else (150, 0, 0) if enemy_type == "fast" else (255, 0, 255)
                pygame.draw.circle(enemy_surface, color, (size//2, size//2), size//2)
                pygame.draw.circle(enemy_surface, (255, 0, 0), (size//2, size//2), size//2 - 5)
                assets['enemies'][enemy_type] = enemy_surface
                pygame.image.save(enemy_surface, enemy_path)

        # Bullet textures
        bullet_types = ["basic", "shotgun", "boss"]
        assets['bullets'] = {}
        for bullet_type in bullet_types:
            bullet_path = os.path.join(assets_folder, f"bullet_{bullet_type}.png")
            if os.path.exists(bullet_path):
                assets['bullets'][bullet_type] = pygame.image.load(bullet_path).convert_alpha()
            else:
                # Create procedural bullet textures
                size = 10 if bullet_type == "basic" else 8 if bullet_type == "shotgun" else 15
                bullet_surface = pygame.Surface((size*2, size*2), pygame.SRCALPHA)
                color = (255, 255, 0) if bullet_type == "basic" else (255, 200, 0) if bullet_type == "shotgun" else (255, 0, 255)
                pygame.draw.circle(bullet_surface, color, (size, size), size)
                pygame.draw.circle(bullet_surface, (255, 255, 255), (size, size), size//2)
                assets['bullets'][bullet_type] = bullet_surface
                pygame.image.save(bullet_surface, bullet_path)

        return assets
    except Exception as e:
        print(f"Error loading assets: {e}")
        return {}

# Načtení assetů
game_assets = load_assets()

# --- Herní proměnné ---
game_state = STATE_SPLASH
score = 0
high_score = load_highscore()
player_stats = {
    'x': float(PLAYER_START_X), 'y': float(PLAYER_START_Y),
    'health': PLAYER_MAX_HEALTH, 'max_health': PLAYER_MAX_HEALTH,
    'light_radius': INITIAL_LIGHT_RADIUS,
    'speed_multiplier': 1.0, 'fire_rate_multiplier': 1.0,
    'bullet_damage': BASE_BULLET_DAMAGE,
    'light_to_add': 0.0, # Zásobník světla pro animaci
    'current_weapon': WEAPON_PISTOL,  # Aktuálně vybraná zbraň
    'weapons': [0, 1, 2],  # Indexy dostupných zbraní
    'poisoned': False,     # Zda je hráč otráven
    'poison_timer': 0.0,   # Zbývající čas otravy
    'slowed': False,       # Zda je hráč zpomalen
    'slow_timer': 0.0,     # Zbývající čas zpomalení
    'challenges_completed': 0,  # Počet splněných výzev
    'weapon_muzzle_x': 0,  # Pozice konce hlavně zbraně pro střelbu
    'weapon_muzzle_y': 0   # Aktualizuje se při vykreslování zbraně
}
shop_item_rects = []
settings_tabs = ["Graphics", "Audio", "Controls", "Gameplay"]
current_settings_tab = 0

# --- Pasti a výzvy ---
traps = []  # Seznam pastí ve hře
challenges = [
    # Základní výzvy
    {
        'name': 'Survive 5 waves',
        'description': 'Survive 5 waves without dying',
        'completed': False,
        'reward': 200,  # Odměna ve skóre
        'check': lambda stats, wave: wave >= 5
    },
    {
        'name': 'Kill 50 enemies',
        'description': 'Kill 50 enemies in total',
        'completed': False,
        'reward': 300,
        'kills_required': 50,
        'kills_current': 0
    },
    {
        'name': 'Dodge 10 traps',
        'description': 'Avoid stepping on 10 traps',
        'completed': False,
        'reward': 250,
        'traps_required': 10,
        'traps_current': 0
    },

    # Nové výzvy
    {
        'name': 'Shotgun Master',
        'description': 'Kill 25 enemies with shotgun',
        'completed': False,
        'reward': 350,
        'kills_required': 25,
        'kills_current': 0,
        'weapon_type': WEAPON_SHOTGUN
    },
    {
        'name': 'Boss Slayer',
        'description': 'Defeat a boss',
        'completed': False,
        'reward': 500,
        'boss_kills_required': 1,
        'boss_kills_current': 0
    },
    {
        'name': 'Light Keeper',
        'description': 'Reach 500 light radius',
        'completed': False,
        'reward': 400,
        'check': lambda stats, wave: stats['light_radius'] >= 500
    }
]

# Funkce pro vytvoření pasti
def create_trap(x, y, trap_type):
    if trap_type == TRAP_TYPE_SPIKE:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_SPIKE,
            'rect': pygame.Rect(x - TRAP_SPIKE_SIZE/2, y - TRAP_SPIKE_SIZE/2, TRAP_SPIKE_SIZE, TRAP_SPIKE_SIZE),
            'damage': TRAP_SPIKE_DAMAGE,
            'triggered': False,
            'color': TRAP_SPIKE_COLOR
        }
    elif trap_type == TRAP_TYPE_POISON:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_POISON,
            'rect': pygame.Rect(x - TRAP_POISON_SIZE/2, y - TRAP_POISON_SIZE/2, TRAP_POISON_SIZE, TRAP_POISON_SIZE),
            'damage': TRAP_POISON_DAMAGE,
            'duration': TRAP_POISON_DURATION,
            'triggered': False,
            'color': TRAP_POISON_COLOR
        }
    elif trap_type == TRAP_TYPE_SLOW:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_SLOW,
            'rect': pygame.Rect(x - TRAP_SLOW_SIZE/2, y - TRAP_SLOW_SIZE/2, TRAP_SLOW_SIZE, TRAP_SLOW_SIZE),
            'slow_factor': TRAP_SLOW_FACTOR,
            'duration': TRAP_SLOW_DURATION,
            'triggered': False,
            'color': TRAP_SLOW_COLOR
        }
    elif trap_type == TRAP_TYPE_EXPLOSION:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_EXPLOSION,
            'rect': pygame.Rect(x - TRAP_EXPLOSION_SIZE/2, y - TRAP_EXPLOSION_SIZE/2, TRAP_EXPLOSION_SIZE, TRAP_EXPLOSION_SIZE),
            'damage': TRAP_EXPLOSION_DAMAGE,
            'triggered': False,
            'color': TRAP_EXPLOSION_COLOR
        }
    elif trap_type == TRAP_TYPE_TELEPORT:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_TELEPORT,
            'rect': pygame.Rect(x - TRAP_TELEPORT_SIZE/2, y - TRAP_TELEPORT_SIZE/2, TRAP_TELEPORT_SIZE, TRAP_TELEPORT_SIZE),
            'triggered': False,
            'color': TRAP_TELEPORT_COLOR
        }
    return None

# Inicializace spatial hash gridu
spatial_grid = SpatialHashGrid(cell_size=50)

# Inicializace object poolů
def create_bullet():
    return {'x': 0.0, 'y': 0.0, 'vx': 0.0, 'vy': 0.0,
            'rect': pygame.Rect(0, 0, BULLET_SIZE, BULLET_SIZE),
            'damage': 0}

def create_boss_bullet():
    return {'x': 0.0, 'y': 0.0, 'vx': 0.0, 'vy': 0.0,
            'rect': pygame.Rect(0, 0, BOSS_PROJECTILE_SIZE, BOSS_PROJECTILE_SIZE),
            'damage': 0}

bullet_pool = ObjectPool(create_bullet, max_size=200)
boss_bullet_pool = ObjectPool(create_boss_bullet, max_size=100)

# Optimalizace vykreslování
last_render_time = 0
RENDER_INTERVAL = 1.0 / 60.0  # 60 FPS target

# --- Pomocné funkce pro UI ---
def draw_text(surface, text, font, color, center_pos, with_bg=False, bg_color=(0, 0, 0, 150), padding=5):
    text_surface = font.render(text, True, color)
    text_rect = text_surface.get_rect(center=center_pos)
    surface.blit(text_surface, text_rect)

def draw_text_topleft(surface, text, font, color, topleft_pos):
    text_surface = font.render(text, True, color)
    surface.blit(text_surface, topleft_pos)

def draw_button(surface, text, font, text_color, rect, base_color, hover_color, border_color=WHITE, border_width=2, disabled=False, disabled_color=GREY):
    mouse_pos = pygame.mouse.get_pos()
    is_hovering = rect.collidepoint(mouse_pos) and not disabled
    current_color = disabled_color if disabled else hover_color if is_hovering else base_color
    current_text_color = GREY if disabled else text_color
    current_border_color = DARK_GREY if disabled else border_color

    pygame.draw.rect(surface, current_color, rect, border_radius=5)
    if border_width > 0:
        pygame.draw.rect(surface, current_border_color, rect, border_width, border_radius=5)
    if text:
        draw_text(surface, text, font, current_text_color, rect.center)

    return is_hovering

def draw_slider(surface, rect, value, min_val, max_val, base_color, fill_color, border_color=WHITE, border_width=2):
    # Vykreslení pozadí slideru
    pygame.draw.rect(surface, base_color, rect, border_radius=3)
    if border_width > 0:
        pygame.draw.rect(surface, border_color, rect, border_width, border_radius=3)

    # Výpočet pozice a velikosti naplnění
    fill_width = int((value - min_val) / (max_val - min_val) * rect.width)
    fill_rect = pygame.Rect(rect.x, rect.y, fill_width, rect.height)

    # Vykreslení naplnění
    pygame.draw.rect(surface, fill_color, fill_rect, border_radius=3)

    # Vykreslení posuvníku
    handle_size = min(rect.height + 6, 20)
    handle_x = rect.x + fill_width - handle_size // 2
    handle_y = rect.y + rect.height // 2 - handle_size // 2
    handle_rect = pygame.Rect(handle_x, handle_y, handle_size, handle_size)
    pygame.draw.rect(surface, WHITE, handle_rect, border_radius=handle_size // 2)

    return handle_rect

def draw_checkbox(surface, rect, checked, base_color, check_color, border_color=WHITE, border_width=2):
    # Vykreslení pozadí checkboxu
    pygame.draw.rect(surface, base_color, rect, border_radius=3)
    if border_width > 0:
        pygame.draw.rect(surface, border_color, rect, border_width, border_radius=3)

    # Vykreslení zaškrtnutí
    if checked:
        inner_rect = pygame.Rect(rect.x + rect.width * 0.2, rect.y + rect.height * 0.2,
                                rect.width * 0.6, rect.height * 0.6)
        pygame.draw.rect(surface, check_color, inner_rect, border_radius=2)

    return rect

def draw_dropdown(surface, rect, options, selected_index, base_color, hover_color, text_color, font, border_color=WHITE, border_width=2, open=False):
    # Vykreslení hlavního tlačítka
    pygame.draw.rect(surface, base_color, rect, border_radius=5)
    if border_width > 0:
        pygame.draw.rect(surface, border_color, rect, border_width, border_radius=5)

    # Vykreslení textu a šipky
    selected_text = options[selected_index]
    draw_text(surface, selected_text, font, text_color, (rect.centerx - 10, rect.centery))

    # Šipka dolů/nahoru
    arrow_points = []
    if open:
        # Šipka nahoru
        arrow_points = [(rect.right - 20, rect.centery + 5),
                        (rect.right - 10, rect.centery - 5),
                        (rect.right - 30, rect.centery - 5)]
    else:
        # Šipka dolů
        arrow_points = [(rect.right - 20, rect.centery - 5),
                        (rect.right - 10, rect.centery + 5),
                        (rect.right - 30, rect.centery + 5)]

    pygame.draw.polygon(surface, text_color, arrow_points)

    # Pokud je rozbalovací seznam otevřený, vykreslit možnosti
    option_rects = []
    if open:
        for i, option in enumerate(options):
            option_rect = pygame.Rect(rect.x, rect.y + rect.height + i * rect.height, rect.width, rect.height)
            mouse_pos = pygame.mouse.get_pos()
            is_hovering = option_rect.collidepoint(mouse_pos)

            pygame.draw.rect(surface, hover_color if is_hovering else base_color, option_rect, border_radius=5)
            if border_width > 0:
                pygame.draw.rect(surface, border_color, option_rect, border_width, border_radius=5)

            draw_text(surface, option, font, text_color, option_rect.center)
            option_rects.append(option_rect)

    return rect, option_rects

def draw_tab_bar(surface, rect, tabs, current_tab, font, active_color, inactive_color, text_color):
    tab_width = rect.width // len(tabs)
    tab_rects = []

    for i, tab in enumerate(tabs):
        tab_rect = pygame.Rect(rect.x + i * tab_width, rect.y, tab_width, rect.height)
        is_active = i == current_tab

        pygame.draw.rect(surface, active_color if is_active else inactive_color, tab_rect, border_radius=5)
        if is_active:
            # Podtržení aktivní záložky
            underline_rect = pygame.Rect(tab_rect.x + 5, tab_rect.bottom - 3, tab_rect.width - 10, 3)
            pygame.draw.rect(surface, text_color, underline_rect, border_radius=2)

        draw_text(surface, tab, font, text_color, tab_rect.center)
        tab_rects.append(tab_rect)

    return tab_rects

# --- Funkce pro vykreslení splash screenu ---
def draw_splash_screen(surface, alpha):
    # Jednoduché černé pozadí
    surface.fill((0, 0, 0))
    
    # Původní logo hry
    font = pygame.font.Font(None, 120)
    text = font.render("LIGHT OR DEAD", True, (255, 255, 255))
    text_rect = text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 50))
    surface.blit(text, text_rect)
    
    # Podtitul
    subtitle_font = pygame.font.Font(None, 48)
    subtitle = subtitle_font.render("Survive the Darkness", True, (200, 200, 200))
    subtitle_rect = subtitle.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
    surface.blit(subtitle, subtitle_rect)
    
    # Výzva k pokračování
    prompt_font = pygame.font.Font(None, 36)
    prompt = prompt_font.render("Click to continue...", True, (180, 180, 180))
    prompt_rect = prompt.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT - 100))
    surface.blit(prompt, prompt_rect)

# --- Funkce pro vykreslení hlavního menu ---
def draw_main_menu(surface, high_score):
    # Create a dynamic background
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
    overlay.fill((0, 0, 0, 200))  # Darker background for better contrast
    
    # Animated background particles
    particle_count = 30
    for i in range(particle_count):
        x = (time.time() * 50 + i * 100) % SCREEN_WIDTH
        y = (time.time() * 30 + i * 50) % SCREEN_HEIGHT
        size = 2 + math.sin(time.time() + i) * 1
        alpha = int(100 + 155 * math.sin(time.time() * 2 + i))
        alpha = max(0, min(255, int(alpha)))
        color = (255, 255, 255, alpha)
        pygame.draw.circle(surface, color, (int(x), int(y)), int(size))
    
    # Enhanced logo with glow
    font_size = 96
    font = pygame.font.Font(None, font_size)
    text = font.render("LIGHT OR DEAD", True, (255, 255, 255))
    text_rect = text.get_rect(center=(SCREEN_WIDTH//2, 100))
    
    # Add text shadow for better readability
    shadow = font.render("LIGHT OR DEAD", True, (0, 0, 0))
    shadow_rect = shadow.get_rect(center=(SCREEN_WIDTH//2 + 2, 102))
    surface.blit(shadow, shadow_rect)
    surface.blit(text, text_rect)
    
    # Draw buttons with hover effects
    button_font = pygame.font.Font(None, 48)
    button_height = 60
    button_width = 300
    button_spacing = 20
    start_y = SCREEN_HEIGHT//2 - button_height
    
    # Start button
    start_rect = pygame.Rect((SCREEN_WIDTH - button_width)//2, start_y, button_width, button_height)
    start_hover = start_rect.collidepoint(pygame.mouse.get_pos())
    start_color = (100, 100, 100) if start_hover else (50, 50, 50)
    pygame.draw.rect(surface, start_color, start_rect)
    pygame.draw.rect(surface, (200, 200, 200), start_rect, 2)
    start_text = button_font.render("Start Game", True, (255, 255, 255))
    start_text_rect = start_text.get_rect(center=start_rect.center)
    surface.blit(start_text, start_text_rect)
    
    # Settings button
    settings_rect = pygame.Rect((SCREEN_WIDTH - button_width)//2, start_y + button_height + button_spacing, 
                              button_width, button_height)
    settings_hover = settings_rect.collidepoint(pygame.mouse.get_pos())
    settings_color = (100, 100, 100) if settings_hover else (50, 50, 50)
    pygame.draw.rect(surface, settings_color, settings_rect)
    pygame.draw.rect(surface, (200, 200, 200), settings_rect, 2)
    settings_text = button_font.render("Settings", True, (255, 255, 255))
    settings_text_rect = settings_text.get_rect(center=settings_rect.center)
    surface.blit(settings_text, settings_text_rect)
    
    # Quit button
    quit_rect = pygame.Rect((SCREEN_WIDTH - button_width)//2, 
                          start_y + (button_height + button_spacing) * 2,
                          button_width, button_height)
    quit_hover = quit_rect.collidepoint(pygame.mouse.get_pos())
    quit_color = (100, 100, 100) if quit_hover else (50, 50, 50)
    pygame.draw.rect(surface, quit_color, quit_rect)
    pygame.draw.rect(surface, (200, 200, 200), quit_rect, 2)
    quit_text = button_font.render("Quit", True, (255, 255, 255))
    quit_text_rect = quit_text.get_rect(center=quit_rect.center)
    surface.blit(quit_text, quit_text_rect)
    
    # High score display
    score_font = pygame.font.Font(None, 36)
    score_text = score_font.render(f"High Score: {high_score}", True, (200, 200, 200))
    score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT - 50))
    surface.blit(score_text, score_rect)
    
    # Apply the overlay
    surface.blit(overlay, (0, 0))
    
    return start_hover, settings_hover, quit_hover

def draw_shop_menu(surface, player_stats, weapons):
    # Create a semi-transparent background
    shop_bg = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
    shop_bg.fill((0, 0, 0, 200))
    surface.blit(shop_bg, (0, 0))

    # Shop title with glow effect
    title_text = "WEAPON SHOP"
    title_font = pygame.font.Font(None, 72)
    title_color = (255, 220, 150)

    # Glow effect for title
    for i in range(6):
        glow_alpha = 80 - i * 12
        glow_color = (*title_color, glow_alpha)
        offset = i * 2
        draw_text(surface, title_text, title_font, glow_color,
                 (SCREEN_WIDTH // 2 + offset, 80 + offset))

    # Main title
    draw_text(surface, title_text, title_font, title_color, (SCREEN_WIDTH // 2, 80))

    # Player stats display
    stats_bg = pygame.Surface((300, 100), pygame.SRCALPHA)
    stats_bg.fill((0, 0, 0, 150))
    surface.blit(stats_bg, (SCREEN_WIDTH - 320, 20))

    # Stats text
    stats_font = pygame.font.Font(None, 32)
    draw_text(surface, f"Score: {score}", stats_font, YELLOW, (SCREEN_WIDTH - 170, 40))
    draw_text(surface, f"Light: {int(player_stats['light_radius'])}", stats_font, WHITE, (SCREEN_WIDTH - 170, 80))

    # Shop grid layout
    grid_start_x = 50
    grid_start_y = 150
    item_width = 250
    item_height = 120
    items_per_row = 3
    spacing = 20

    # Vykreslení položek v obchodě
    for i, item in enumerate(shop_items):
        row = i // items_per_row
        col = i % items_per_row
        x = grid_start_x + col * (item_width + spacing)
        y = grid_start_y + row * (item_height + spacing)
        
        # Pozadí položky
        item_rect = pygame.Rect(x, y, item_width, item_height)
        pygame.draw.rect(surface, (30, 30, 40), item_rect)
        pygame.draw.rect(surface, WHITE, item_rect, 2)
        
        # Název položky
        name_font = pygame.font.Font(None, 24)
        name_text = name_font.render(item['name'], True, WHITE)
        name_rect = name_text.get_rect(center=(x + item_width//2, y + 30))
        surface.blit(name_text, name_rect)
        
        # Popis položky s počtem upgradů
        desc_font = pygame.font.Font(None, 20)
        if 'max_upgrades' in item:
            current = 0
            if item['id'] == 'firerate':
                current = int((player_stats['fire_rate_multiplier'] - 1.0) / 0.15)
            elif item['id'] == 'move_speed':
                current = int((player_stats['speed_multiplier'] - 1.0) / 0.15)
            elif item['id'] == 'bullet_dmg':
                current = player_stats['bullet_damage'] - 2  # Upraveno pro nový BASE_BULLET_DAMAGE
            elif item['id'] == 'light':
                current = int(player_stats['light_to_add'] / 50)
            desc_text = f"{item['desc'].split('(')[0]}({current}/{item['max_upgrades']})"
        else:
            desc_text = item['desc']
        desc_surface = desc_font.render(desc_text, True, WHITE)
        desc_rect = desc_surface.get_rect(center=(x + item_width//2, y + 60))
        surface.blit(desc_surface, desc_rect)
        
        # Cena
        cost_font = pygame.font.Font(None, 28)
        cost_text = cost_font.render(f"{item['cost']} pts", True, WHITE)
        cost_rect = cost_text.get_rect(center=(x + item_width//2, y + item_height - 40))
        surface.blit(cost_text, cost_rect)
        
        # Tlačítko pro nákup
        button_rect = pygame.Rect(x + 20, y + item_height - 80, item_width - 40, 30)
        pygame.draw.rect(surface, (0, 100, 0), button_rect)
        pygame.draw.rect(surface, WHITE, button_rect, 2)
        
        button_font = pygame.font.Font(None, 20)
        button_text = button_font.render("BUY", True, WHITE)
        button_text_rect = button_text.get_rect(center=button_rect.center)
        surface.blit(button_text, button_text_rect)
        
        # Kontrola, zda je položka dostupná
        if 'max_upgrades' in item:
            current = 0
            if item['id'] == 'firerate':
                current = int((player_stats['fire_rate_multiplier'] - 1.0) / 0.15)
            elif item['id'] == 'move_speed':
                current = int((player_stats['speed_multiplier'] - 1.0) / 0.15)
            elif item['id'] == 'bullet_dmg':
                current = player_stats['bullet_damage'] - 2  # Upraveno pro nový BASE_BULLET_DAMAGE
            elif item['id'] == 'light':
                current = int(player_stats['light_to_add'] / 50)
                
            if current >= item['max_upgrades']:
                # Překřížení položky, pokud je dosaženo maxima
                overlay = pygame.Surface((item_width, item_height), pygame.SRCALPHA)
                overlay.fill((0, 0, 0, 128))
                surface.blit(overlay, (x, y))
                max_text = button_font.render("MAX", True, WHITE)
                max_rect = max_text.get_rect(center=(x + item_width//2, y + item_height//2))
                surface.blit(max_text, max_rect)

    # Exit button
    exit_button_rect = pygame.Rect(SCREEN_WIDTH // 2 - 100, SCREEN_HEIGHT - 80, 200, 50)
    
    # Button background with gradient
    for y in range(50):
        color = (40, 40, 50, 200)
        pygame.draw.line(surface, color,
                        (exit_button_rect.left, exit_button_rect.top + y),
                        (exit_button_rect.right, exit_button_rect.top + y))

    # Button border
    pygame.draw.rect(surface, WHITE, exit_button_rect, 2, border_radius=10)

    # Button text
    exit_font = pygame.font.Font(None, 36)
    draw_text(surface, "Exit Shop", exit_font, WHITE, exit_button_rect.center)

    return shop_items, exit_button_rect

# --- Funkce pro vykreslení nastavení ---
def draw_settings_menu(surface, settings, current_tab, tabs):
    # Vyplnění pozadí tmavou barvou
    surface.fill(BLACK)

    # Nadpis
    title_text = "Settings"
    title_font = pygame.font.Font(None, 60)
    draw_text(surface, title_text, title_font, WHITE, (SCREEN_WIDTH // 2, 60))

    # Záložky
    tab_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 - 300, 120, 600, 40)
    tab_rects = draw_tab_bar(surface, tab_bar_rect, tabs, current_tab, pygame.font.Font(None, 30),
                            DARK_GREY, BLACK, WHITE)

    # Oblast obsahu
    content_rect = pygame.Rect(SCREEN_WIDTH // 2 - 350, 180, 700, 350)
    pygame.draw.rect(surface, DARK_GREY, content_rect, border_radius=5)
    pygame.draw.rect(surface, WHITE, content_rect, 2, border_radius=5)

    # Tlačítko zpět
    back_button_rect = pygame.Rect(SCREEN_WIDTH // 2 - 100, SCREEN_HEIGHT - 70, 200, 50)
    back_hover = draw_button(surface, "Back", pygame.font.Font(None, 36), BLACK, back_button_rect, RED, WHITE)

    # Obsah podle aktuální záložky
    if current_tab == 0:  # Graphics
        # Rozlišení
        draw_text(surface, "Resolution:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 50))
        resolution_options = ["800x600", "1024x768", "1280x720", "1920x1080"]
        resolution_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 35, 250, 30)
        resolution_dropdown, resolution_options_rects = draw_dropdown(surface, resolution_rect, resolution_options,
                                                                    0, DARK_GREY, GREY, WHITE,
                                                                    pygame.font.Font(None, 24))

        # Kvalita
        draw_text(surface, "Quality:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 100))
        quality_options = ["Low", "Medium", "High"]
        quality_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 85, 250, 30)
        quality_dropdown, quality_options_rects = draw_dropdown(surface, quality_rect, quality_options,
                                                              settings['quality'], DARK_GREY, GREY, WHITE,
                                                              pygame.font.Font(None, 24))

        # VSync
        draw_text(surface, "VSync:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 150))
        vsync_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 140, 30, 30)
        vsync_checkbox = draw_checkbox(surface, vsync_rect, settings['vsync'], DARK_GREY, GREEN)

        # FPS Counter
        draw_text(surface, "Show FPS:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 200))
        fps_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 190, 30, 30)
        fps_checkbox = draw_checkbox(surface, fps_rect, settings['show_fps'], DARK_GREY, GREEN)

    elif current_tab == 1:  # Audio
        # Hlavní hlasitost
        draw_text(surface, "Master Volume:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 50))
        volume_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 45, 250, 20)
        volume_slider = draw_slider(surface, volume_rect, settings['volume'], 0.0, 1.0, DARK_GREY, GREEN)

        # Hlasitost hudby
        draw_text(surface, "Music Volume:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 100))
        music_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 95, 250, 20)
        music_slider = draw_slider(surface, music_rect, settings['music_volume'], 0.0, 1.0, DARK_GREY, GREEN)

    elif current_tab == 2:  # Controls
        # Klávesy pro pohyb
        draw_text(surface, "Move Up:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 50))
        draw_text(surface, pygame.key.name(settings['keybinds']['move_up']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 50))

        draw_text(surface, "Move Down:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 90))
        draw_text(surface, pygame.key.name(settings['keybinds']['move_down']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 90))

        draw_text(surface, "Move Left:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 130))
        draw_text(surface, pygame.key.name(settings['keybinds']['move_left']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 130))

        draw_text(surface, "Move Right:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 170))
        draw_text(surface, pygame.key.name(settings['keybinds']['move_right']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 170))

        # Klávesy pro zbraně
        draw_text(surface, "Weapon 1:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 210))
        draw_text(surface, pygame.key.name(settings['keybinds']['weapon_1']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 210))

        draw_text(surface, "Weapon 2:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 250))
        draw_text(surface, pygame.key.name(settings['keybinds']['weapon_2']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 250))

        draw_text(surface, "Weapon 3:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 290))
        draw_text(surface, pygame.key.name(settings['keybinds']['weapon_3']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 290))

    elif current_tab == 3:  # Gameplay
        # Obtížnost
        draw_text(surface, "Difficulty:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 50))
        difficulty_options = ["Easy", "Normal", "Hard"]
        difficulty_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 35, 250, 30)
        difficulty_dropdown, difficulty_options_rects = draw_dropdown(surface, difficulty_rect, difficulty_options,
                                                                    1, DARK_GREY, GREY, WHITE,
                                                                    pygame.font.Font(None, 24))

        # Další nastavení hry...

    return back_hover, tab_rects

# --- Spawn Minionů (Nová Funkce) ---
def spawn_minions(boss_x, boss_y, count, wave_num):
    global enemies, enemies_alive_in_wave
    print(f"Boss summoning {count} minions...")
    # Minioni mají snížené HP oproti běžným nepřátelům dané vlny
    minion_health = int((ENEMY_BASE_HEALTH + int(wave_num * 0.8)) * BOSS_SUMMON_HEALTH_FACTOR)
    minion_health = max(1, minion_health) # Alespoň 1 HP
    # Ostatní staty mohou být stejné jako u běžných nepřátel dané vlny nebo upravené
    minion_speed = ENEMY_BASE_SPEED + wave_num * 4.0 + (wave_num // 4) * 5.0
    minion_damage = ENEMY_BASE_DAMAGE + int(wave_num * 0.5)
    minion_score = int((ENEMY_BASE_SCORE + wave_num) * 0.5) # Menší skóre za miniony

    spawn_radius = BOSS_SIZE * 1.5 # Jak daleko od bosse se spawnují

    for _ in range(count):
        # Náhodný úhel a vzdálenost od bosse
        angle = random.uniform(0, 2 * math.pi)
        dist = random.uniform(BOSS_SIZE * 0.6, spawn_radius)
        ex = boss_x + math.cos(angle) * dist
        ey = boss_y + math.sin(angle) * dist

        # Omezení pozice na obrazovce (pro jistotu)
        ex = max(ENEMY_BASE_SIZE / 2, min(SCREEN_WIDTH - ENEMY_BASE_SIZE / 2, ex))
        ey = max(ENEMY_BASE_SIZE / 2, min(SCREEN_HEIGHT - ENEMY_BASE_SIZE / 2, ey))

        enemies.append({
            'x': float(ex), 'y': float(ey),
            'rect': pygame.Rect(ex - ENEMY_BASE_SIZE / 2, ey - ENEMY_BASE_SIZE / 2, ENEMY_BASE_SIZE, ENEMY_BASE_SIZE),
            'max_health': minion_health, 'health': minion_health,
            'speed': minion_speed, 'damage': minion_damage,
            'score': minion_score,
            'is_boss': False, # Není boss
            'is_minion': True, # Označení miniona
            'size': ENEMY_BASE_SIZE
        })
        enemies_alive_in_wave += 1 # Zvýšíme počet živých nepřátel

# --- Wave Management (Upraveno pro Boss Wave & Scaling) ---
def start_wave(wave_num):
    global current_wave, enemies_to_spawn_in_wave, enemies_alive_in_wave, wave_message_timer, wave_message_text, enemies, score, traps # Přidáno score pro škálování a traps
    current_wave = wave_num
    enemies.clear()
    boss_bullets.clear() # Vyčistit střely bosse z minulé vlny
    spawn_margin = 50

    # Generování pastí pro tuto vlnu
    num_traps = random.randint(1, 3 + current_wave // 2)  # Více pastí ve vyšších vlnách
    for _ in range(num_traps):
        if random.random() < TRAP_SPAWN_CHANCE:
            # Náhodná pozice na obrazovce (ne příliš blízko hráče)
            safe_distance = 150  # Minimální vzdálenost od hráče
            while True:
                trap_x = random.randint(50, SCREEN_WIDTH - 50)
                trap_y = random.randint(50, SCREEN_HEIGHT - 50)

                # Kontrola vzdálenosti od hráče
                dx = trap_x - player_stats['x']
                dy = trap_y - player_stats['y']
                dist = math.sqrt(dx*dx + dy*dy)

                if dist > safe_distance:
                    break

            # Náhodný typ pasti
            trap_type = random.randint(0, 2)  # 0=bodce, 1=jed, 2=zpomalení
            new_trap = create_trap(trap_x, trap_y, trap_type)
            if new_trap:
                traps.append(new_trap)
                print(f"Spawned trap type {trap_type} at ({trap_x}, {trap_y})")

    is_boss_wave = (wave_num > 0 and wave_num % BOSS_WAVE_INTERVAL == 0)

    if is_boss_wave:
        enemies_to_spawn_in_wave = 1
        enemies_alive_in_wave = 1
        wave_message_text = f"WAVE {wave_num} - BOSS WAVE!"

        base_health_this_wave = ENEMY_BASE_HEALTH + int(wave_num * 0.8)
        base_speed_this_wave = ENEMY_BASE_SPEED + wave_num * 4.0 + (wave_num // 4) * 5.0
        base_damage_this_wave = ENEMY_BASE_DAMAGE + int(wave_num * 0.5)
        base_score_this_wave = ENEMY_BASE_SCORE + wave_num

        # <<< Škálování HP bosse i podle skóre hráče >>>
        score_hp_bonus = int((score / 100) * BOSS_SCORE_HP_FACTOR) # Příklad: +1 HP za každých 1000 bodů score
        boss_health = int(base_health_this_wave * BOSS_HEALTH_MULTIPLIER) + score_hp_bonus
        boss_health = max(boss_health, base_health_this_wave * 5) # Zajistíme minimální HP
        # --- Konec škálování HP ---

        boss_speed = base_speed_this_wave * BOSS_SPEED_MULTIPLIER
        boss_damage = int(base_damage_this_wave * BOSS_DAMAGE_MULTIPLIER)
        boss_score = base_score_this_wave * BOSS_SCORE_MULTIPLIER

        print(f"Starting BOSS WAVE {wave_num}: HP:{boss_health} (Score Bonus: {score_hp_bonus}) S:{boss_speed:.1f} D:{boss_damage} Score:{boss_score}")

        ex, ey = SCREEN_WIDTH / 2, -spawn_margin # Spawn uprostřed nahoře

        enemies.append({
            'x': float(ex), 'y': float(ey),
            'rect': pygame.Rect(ex - BOSS_SIZE / 2, ey - BOSS_SIZE / 2, BOSS_SIZE, BOSS_SIZE),
            'max_health': boss_health, 'health': boss_health,
            'speed': boss_speed, 'damage': boss_damage, 'score': boss_score,
            'is_boss': True, 'size': BOSS_SIZE,
            # Stavy pro mechaniky bosse
            'action_cooldown': random.uniform(1.0, BOSS_ACTION_COOLDOWN_MIN), # První akce dříve
            'is_dashing': False, 'dash_timer': 0.0,
            'dash_target_x': 0.0, 'dash_target_y': 0.0
        })

    else: # Regular Wave
        enemies_to_spawn_in_wave = 5 + int(wave_num * 1.5 + (wave_num // (BOSS_WAVE_INTERVAL + 1)) * 3)
        wave_enemy_health = ENEMY_BASE_HEALTH + int(wave_num * 0.8)
        wave_enemy_speed = ENEMY_BASE_SPEED + wave_num * 4.0 + (wave_num // 4) * 5.0
        wave_enemy_damage = ENEMY_BASE_DAMAGE + int(wave_num * 0.5)
        wave_enemy_score = ENEMY_BASE_SCORE + wave_num
        print(f"Starting Wave {wave_num}: Spawning {enemies_to_spawn_in_wave} | H:{wave_enemy_health} S:{wave_enemy_speed:.1f} D:{wave_enemy_damage}")
        enemies_alive_in_wave = enemies_to_spawn_in_wave
        wave_message_text = f"Wave {current_wave}"

        for _ in range(enemies_to_spawn_in_wave):
            side = random.choice(['top','bottom','left','right'])
            if side=='top': ex,ey=random.uniform(0,SCREEN_WIDTH),-spawn_margin
            elif side=='bottom': ex,ey=random.uniform(0,SCREEN_WIDTH),SCREEN_HEIGHT+spawn_margin
            elif side=='left': ex,ey=-spawn_margin,random.uniform(0,SCREEN_HEIGHT)
            else: ex,ey=SCREEN_WIDTH+spawn_margin,random.uniform(0,SCREEN_HEIGHT)
            enemies.append({
                'x':float(ex), 'y':float(ey),
                'rect':pygame.Rect(ex - ENEMY_BASE_SIZE / 2, ey - ENEMY_BASE_SIZE / 2, ENEMY_BASE_SIZE, ENEMY_BASE_SIZE),
                'max_health':wave_enemy_health, 'health':wave_enemy_health,
                'speed':wave_enemy_speed, 'damage':wave_enemy_damage,
                'score':wave_enemy_score,
                'is_boss': False, 'is_minion': False, # Označení běžného nepřítele
                'size': ENEMY_BASE_SIZE
            })

    wave_message_timer = WAVE_MESSAGE_DURATION

# --- Funkce pro reset hry (Upraveno pro zbraně, pasti a výzvy) ---
def reset_game():
    global player_stats, score, high_score, enemies, bullets, boss_bullets, last_shot_time, game_state, current_wave, enemies_alive_in_wave, wave_transition_timer, wave_message_timer, wave_message_text, weapons, traps, challenges

    # Reset zbraní - pouze pistole je vlastněná na začátku
    weapons[WEAPON_PISTOL]['owned'] = True
    weapons[WEAPON_SHOTGUN]['owned'] = False
    weapons[WEAPON_MACHINEGUN]['owned'] = False

    # Reset pastí
    traps.clear()

    # Reset výzev
    for challenge in challenges:
        challenge['completed'] = False
        if 'kills_current' in challenge:
            challenge['kills_current'] = 0
        if 'traps_current' in challenge:
            challenge['traps_current'] = 0

    player_stats = {
        'x': float(PLAYER_START_X),
        'y': float(PLAYER_START_Y),
        'health': PLAYER_MAX_HEALTH,
        'max_health': PLAYER_MAX_HEALTH,
        'light_radius': INITIAL_LIGHT_RADIUS,
        'speed_multiplier': 1.0,
        'fire_rate_multiplier': 1.0,
        'bullet_damage': BASE_BULLET_DAMAGE,
        'light_to_add': 0.0,
        'current_weapon': WEAPON_PISTOL,  # Začínáme s pistolí
        'weapons': [0, 1, 2],  # Indexy dostupných zbraní
        'poisoned': False,     # Zda je hráč otráven
        'poison_timer': 0.0,   # Zbývající čas otravy
        'slowed': False,       # Zda je hráč zpomalen
        'slow_timer': 0.0,     # Zbývající čas zpomalení
        'challenges_completed': 0  # Počet splněných výzev
    }

    score = 0
    high_score = load_highscore()
    enemies.clear()
    bullets.clear()
    boss_bullets.clear() # Vyčistit i střely bosse
    last_shot_time = 0
    current_wave = 0
    enemies_alive_in_wave = 0
    wave_transition_timer = 0.0 # Začínáme hned
    wave_message_timer = WAVE_MESSAGE_DURATION
    wave_message_text = "Get Ready!"
    game_state = STATE_PLAYING
    print("Game reset and started.")

# --- Performance Monitoring ---
class PerformanceMonitor:
    def __init__(self, window_size=60):  # 60 frames window for averaging
        self.frame_times = []
        self.window_size = window_size
        self.last_time = time.time()
        self.fps_history = []
        self.current_fps = 0

    def update(self):
        current_time = time.time()
        dt = current_time - self.last_time
        self.last_time = current_time

        self.frame_times.append(dt)
        if len(self.frame_times) > self.window_size:
            self.frame_times.pop(0)

        if len(self.frame_times) > 0:
            avg_frame_time = sum(self.frame_times) / len(self.frame_times)
            self.current_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
            self.fps_history.append(self.current_fps)
            if len(self.fps_history) > 10:  # Keep only last 10 FPS readings
                self.fps_history.pop(0)

    def get_fps(self):
        return self.current_fps

    def get_avg_fps(self):
        if len(self.fps_history) > 0:
            return sum(self.fps_history) / len(self.fps_history)
        return 0

# --- Herní smyčka ---
running = True; current_timestamp = time.time()
glEnable(GL_BLEND); glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
print("--- Start main loop ---")

# Inicializace performance monitoru
perf_monitor = PerformanceMonitor()
show_performance = settings['show_fps']  # Přepínač pro zobrazení výkonu

# Inicializace splash screenu
splash_start_time = time.time()
splash_alpha = 0

# Proměnné pro dropdown menu
dropdown_open = False
selected_dropdown = None

class SoundManager:
    def __init__(self):
        self.sounds = {}
        self.music = None
        self.volume = 0.7
        self.music_volume = 0.5
        
    def load_sounds(self):
        sounds_dir = "game_assets/sounds"
        if not os.path.exists(sounds_dir):
            os.makedirs(sounds_dir)
            
        # Seznam zvuků k načtení
        sound_files = {
            'shoot': 'shoot.wav',
            'hit': 'hit.wav',
            'enemy_death': 'enemy_death.wav',
            'player_hurt': 'player_hurt.wav',
            'pickup': 'pickup.wav',
            'button_click': 'button_click.wav',
            'wave_start': 'wave_start.wav',
            'game_over': 'game_over.wav',
            'boss_spawn': 'boss_spawn.wav',
            'boss_death': 'boss_death.wav',
            'shop_buy': 'shop_buy.wav',
            'shop_error': 'shop_error.wav',
            'trap_trigger': 'trap_trigger.wav',
            'powerup': 'powerup.wav'
        }
        
        # Načtení zvuků
        for sound_name, filename in sound_files.items():
            filepath = os.path.join(sounds_dir, filename)
            if os.path.exists(filepath):
                self.sounds[sound_name] = pygame.mixer.Sound(filepath)
                self.sounds[sound_name].set_volume(self.volume)
            else:
                print(f"Warning: Sound file {filename} not found")
                
    def play_sound(self, sound_name):
        if sound_name in self.sounds:
            self.sounds[sound_name].play()
            
    def set_volume(self, volume):
        self.volume = max(0.0, min(1.0, volume))
        for sound in self.sounds.values():
            sound.set_volume(self.volume)
            
    def set_music_volume(self, volume):
        self.music_volume = max(0.0, min(1.0, volume))
        if self.music:
            self.music.set_volume(self.music_volume)
            
    def play_music(self, music_file):
        if os.path.exists(music_file):
            pygame.mixer.music.load(music_file)
            pygame.mixer.music.set_volume(self.music_volume)
            pygame.mixer.music.play(-1)  # -1 znamená opakované přehrávání
            self.music = pygame.mixer.music
            
    def stop_music(self):
        pygame.mixer.music.stop()
        self.music = None

# Vytvoření instance SoundManager
sound_manager = SoundManager()

def load_assets():
    assets = {}
    assets_folder = "game_assets"
    
    try:
        # Načtení zvuků
        sound_manager.load_sounds()
        
        # Create assets folder if it doesn't exist
        if not os.path.exists(assets_folder):
            os.makedirs(assets_folder)

        # Player texture
        player_path = os.path.join(assets_folder, "player.png")
        if os.path.exists(player_path):
            assets['player'] = pygame.image.load(player_path).convert_alpha()
        else:
            # Create procedural player texture
            player_surface = pygame.Surface((60, 60), pygame.SRCALPHA)
            # Main body
            pygame.draw.circle(player_surface, (255, 255, 255), (30, 30), 25)
            # Inner glow
            pygame.draw.circle(player_surface, (255, 220, 180), (30, 30), 20)
            # Eyes
            pygame.draw.circle(player_surface, (255, 255, 255), (20, 25), 8)
            pygame.draw.circle(player_surface, (255, 255, 255), (40, 25), 8)
            pygame.draw.circle(player_surface, (0, 0, 0), (20, 25), 4)
            pygame.draw.circle(player_surface, (0, 0, 0), (40, 25), 4)
            assets['player'] = player_surface
            pygame.image.save(player_surface, player_path)

        # Weapon textures
        weapon_types = ["pistol", "shotgun", "machinegun"]
        assets['weapons'] = {}
        for weapon_type in weapon_types:
            weapon_path = os.path.join(assets_folder, f"weapon_{weapon_type}.png")
            if os.path.exists(weapon_path):
                assets['weapons'][weapon_type] = pygame.image.load(weapon_path).convert_alpha()
            else:
                # Create procedural weapon textures
                weapon_surface = pygame.Surface((80, 30), pygame.SRCALPHA)
                if weapon_type == "pistol":
                    # Pistol design
                    pygame.draw.rect(weapon_surface, (100, 100, 100), (0, 10, 40, 15))
                    pygame.draw.rect(weapon_surface, (80, 80, 80), (40, 12, 30, 11))
                elif weapon_type == "shotgun":
                    # Shotgun design
                    pygame.draw.rect(weapon_surface, (120, 120, 120), (0, 5, 50, 20))
                    pygame.draw.rect(weapon_surface, (100, 100, 100), (50, 8, 30, 14))
                else:  # machinegun
                    # Machine gun design
                    pygame.draw.rect(weapon_surface, (90, 90, 90), (0, 8, 60, 14))
                    pygame.draw.rect(weapon_surface, (70, 70, 70), (60, 10, 20, 10))
                assets['weapons'][weapon_type] = weapon_surface
                pygame.image.save(weapon_surface, weapon_path)

        # Enemy textures
        enemy_types = ["basic", "fast", "boss"]
        assets['enemies'] = {}
        for enemy_type in enemy_types:
            enemy_path = os.path.join(assets_folder, f"enemy_{enemy_type}.png")
            if os.path.exists(enemy_path):
                assets['enemies'][enemy_type] = pygame.image.load(enemy_path).convert_alpha()
            else:
                # Create procedural enemy textures
                size = 40 if enemy_type == "basic" else 50 if enemy_type == "fast" else 80
                enemy_surface = pygame.Surface((size, size), pygame.SRCALPHA)
                color = (200, 0, 0) if enemy_type == "basic" else (150, 0, 0) if enemy_type == "fast" else (255, 0, 255)
                pygame.draw.circle(enemy_surface, color, (size//2, size//2), size//2)
                pygame.draw.circle(enemy_surface, (255, 0, 0), (size//2, size//2), size//2 - 5)
                assets['enemies'][enemy_type] = enemy_surface
                pygame.image.save(enemy_surface, enemy_path)

        # Bullet textures
        bullet_types = ["basic", "shotgun", "boss"]
        assets['bullets'] = {}
        for bullet_type in bullet_types:
            bullet_path = os.path.join(assets_folder, f"bullet_{bullet_type}.png")
            if os.path.exists(bullet_path):
                assets['bullets'][bullet_type] = pygame.image.load(bullet_path).convert_alpha()
            else:
                # Create procedural bullet textures
                size = 10 if bullet_type == "basic" else 8 if bullet_type == "shotgun" else 15
                bullet_surface = pygame.Surface((size*2, size*2), pygame.SRCALPHA)
                color = (255, 255, 0) if bullet_type == "basic" else (255, 200, 0) if bullet_type == "shotgun" else (255, 0, 255)
                pygame.draw.circle(bullet_surface, color, (size, size), size)
                pygame.draw.circle(bullet_surface, (255, 255, 255), (size, size), size//2)
                assets['bullets'][bullet_type] = bullet_surface
                pygame.image.save(bullet_surface, bullet_path)

        return assets
    except Exception as e:
        print(f"Error loading assets: {e}")
        return {}

# Načtení assetů
game_assets = load_assets()

# --- Herní proměnné ---
game_state = STATE_SPLASH
score = 0
high_score = load_highscore()
player_stats = {
    'x': float(PLAYER_START_X), 'y': float(PLAYER_START_Y),
    'health': PLAYER_MAX_HEALTH, 'max_health': PLAYER_MAX_HEALTH,
    'light_radius': INITIAL_LIGHT_RADIUS,
    'speed_multiplier': 1.0, 'fire_rate_multiplier': 1.0,
    'bullet_damage': BASE_BULLET_DAMAGE,
    'light_to_add': 0.0, # Zásobník světla pro animaci
    'current_weapon': WEAPON_PISTOL,  # Aktuálně vybraná zbraň
    'weapons': [0, 1, 2],  # Indexy dostupných zbraní
    'poisoned': False,     # Zda je hráč otráven
    'poison_timer': 0.0,   # Zbývající čas otravy
    'slowed': False,       # Zda je hráč zpomalen
    'slow_timer': 0.0,     # Zbývající čas zpomalení
    'challenges_completed': 0,  # Počet splněných výzev
    'weapon_muzzle_x': 0,  # Pozice konce hlavně zbraně pro střelbu
    'weapon_muzzle_y': 0   # Aktualizuje se při vykreslování zbraně
}
shop_item_rects = []
settings_tabs = ["Graphics", "Audio", "Controls", "Gameplay"]
current_settings_tab = 0

# --- Pasti a výzvy ---
traps = []  # Seznam pastí ve hře
challenges = [
    # Základní výzvy
    {
        'name': 'Survive 5 waves',
        'description': 'Survive 5 waves without dying',
        'completed': False,
        'reward': 200,  # Odměna ve skóre
        'check': lambda stats, wave: wave >= 5
    },
    {
        'name': 'Kill 50 enemies',
        'description': 'Kill 50 enemies in total',
        'completed': False,
        'reward': 300,
        'kills_required': 50,
        'kills_current': 0
    },
    {
        'name': 'Dodge 10 traps',
        'description': 'Avoid stepping on 10 traps',
        'completed': False,
        'reward': 250,
        'traps_required': 10,
        'traps_current': 0
    },

    # Nové výzvy
    {
        'name': 'Shotgun Master',
        'description': 'Kill 25 enemies with shotgun',
        'completed': False,
        'reward': 350,
        'kills_required': 25,
        'kills_current': 0,
        'weapon_type': WEAPON_SHOTGUN
    },
    {
        'name': 'Boss Slayer',
        'description': 'Defeat a boss',
        'completed': False,
        'reward': 500,
        'boss_kills_required': 1,
        'boss_kills_current': 0
    },
    {
        'name': 'Light Keeper',
        'description': 'Reach 500 light radius',
        'completed': False,
        'reward': 400,
        'check': lambda stats, wave: stats['light_radius'] >= 500
    }
]

# Funkce pro vytvoření pasti
def create_trap(x, y, trap_type):
    if trap_type == TRAP_TYPE_SPIKE:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_SPIKE,
            'rect': pygame.Rect(x - TRAP_SPIKE_SIZE/2, y - TRAP_SPIKE_SIZE/2, TRAP_SPIKE_SIZE, TRAP_SPIKE_SIZE),
            'damage': TRAP_SPIKE_DAMAGE,
            'triggered': False,
            'color': TRAP_SPIKE_COLOR
        }
    elif trap_type == TRAP_TYPE_POISON:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_POISON,
            'rect': pygame.Rect(x - TRAP_POISON_SIZE/2, y - TRAP_POISON_SIZE/2, TRAP_POISON_SIZE, TRAP_POISON_SIZE),
            'damage': TRAP_POISON_DAMAGE,
            'duration': TRAP_POISON_DURATION,
            'triggered': False,
            'color': TRAP_POISON_COLOR
        }
    elif trap_type == TRAP_TYPE_SLOW:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_SLOW,
            'rect': pygame.Rect(x - TRAP_SLOW_SIZE/2, y - TRAP_SLOW_SIZE/2, TRAP_SLOW_SIZE, TRAP_SLOW_SIZE),
            'slow_factor': TRAP_SLOW_FACTOR,
            'duration': TRAP_SLOW_DURATION,
            'triggered': False,
            'color': TRAP_SLOW_COLOR
        }
    elif trap_type == TRAP_TYPE_EXPLOSION:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_EXPLOSION,
            'rect': pygame.Rect(x - TRAP_EXPLOSION_SIZE/2, y - TRAP_EXPLOSION_SIZE/2, TRAP_EXPLOSION_SIZE, TRAP_EXPLOSION_SIZE),
            'damage': TRAP_EXPLOSION_DAMAGE,
            'triggered': False,
            'color': TRAP_EXPLOSION_COLOR
        }
    elif trap_type == TRAP_TYPE_TELEPORT:
        return {
            'x': x,
            'y': y,
            'type': TRAP_TYPE_TELEPORT,
            'rect': pygame.Rect(x - TRAP_TELEPORT_SIZE/2, y - TRAP_TELEPORT_SIZE/2, TRAP_TELEPORT_SIZE, TRAP_TELEPORT_SIZE),
            'triggered': False,
            'color': TRAP_TELEPORT_COLOR
        }
    return None

# Inicializace spatial hash gridu
spatial_grid = SpatialHashGrid(cell_size=50)

# Inicializace object poolů
def create_bullet():
    return {'x': 0.0, 'y': 0.0, 'vx': 0.0, 'vy': 0.0,
            'rect': pygame.Rect(0, 0, BULLET_SIZE, BULLET_SIZE),
            'damage': 0}

def create_boss_bullet():
    return {'x': 0.0, 'y': 0.0, 'vx': 0.0, 'vy': 0.0,
            'rect': pygame.Rect(0, 0, BOSS_PROJECTILE_SIZE, BOSS_PROJECTILE_SIZE),
            'damage': 0}

bullet_pool = ObjectPool(create_bullet, max_size=200)
boss_bullet_pool = ObjectPool(create_boss_bullet, max_size=100)

# Optimalizace vykreslování
last_render_time = 0
RENDER_INTERVAL = 1.0 / 60.0  # 60 FPS target

# --- Pomocné funkce pro UI ---
def draw_text(surface, text, font, color, center_pos, with_bg=False, bg_color=(0, 0, 0, 150), padding=5):
    text_surface = font.render(text, True, color)
    text_rect = text_surface.get_rect(center=center_pos)
    surface.blit(text_surface, text_rect)

def draw_text_topleft(surface, text, font, color, topleft_pos):
    text_surface = font.render(text, True, color)
    surface.blit(text_surface, topleft_pos)

def draw_button(surface, text, font, text_color, rect, base_color, hover_color, border_color=WHITE, border_width=2, disabled=False, disabled_color=GREY):
    mouse_pos = pygame.mouse.get_pos()
    is_hovering = rect.collidepoint(mouse_pos) and not disabled
    current_color = disabled_color if disabled else hover_color if is_hovering else base_color
    current_text_color = GREY if disabled else text_color
    current_border_color = DARK_GREY if disabled else border_color

    pygame.draw.rect(surface, current_color, rect, border_radius=5)
    if border_width > 0:
        pygame.draw.rect(surface, current_border_color, rect, border_width, border_radius=5)
    if text:
        draw_text(surface, text, font, current_text_color, rect.center)

    return is_hovering

def draw_slider(surface, rect, value, min_val, max_val, base_color, fill_color, border_color=WHITE, border_width=2):
    # Vykreslení pozadí slideru
    pygame.draw.rect(surface, base_color, rect, border_radius=3)
    if border_width > 0:
        pygame.draw.rect(surface, border_color, rect, border_width, border_radius=3)

    # Výpočet pozice a velikosti naplnění
    fill_width = int((value - min_val) / (max_val - min_val) * rect.width)
    fill_rect = pygame.Rect(rect.x, rect.y, fill_width, rect.height)

    # Vykreslení naplnění
    pygame.draw.rect(surface, fill_color, fill_rect, border_radius=3)

    # Vykreslení posuvníku
    handle_size = min(rect.height + 6, 20)
    handle_x = rect.x + fill_width - handle_size // 2
    handle_y = rect.y + rect.height // 2 - handle_size // 2
    handle_rect = pygame.Rect(handle_x, handle_y, handle_size, handle_size)
    pygame.draw.rect(surface, WHITE, handle_rect, border_radius=handle_size // 2)

    return handle_rect

def draw_checkbox(surface, rect, checked, base_color, check_color, border_color=WHITE, border_width=2):
    # Vykreslení pozadí checkboxu
    pygame.draw.rect(surface, base_color, rect, border_radius=3)
    if border_width > 0:
        pygame.draw.rect(surface, border_color, rect, border_width, border_radius=3)

    # Vykreslení zaškrtnutí
    if checked:
        inner_rect = pygame.Rect(rect.x + rect.width * 0.2, rect.y + rect.height * 0.2,
                                rect.width * 0.6, rect.height * 0.6)
        pygame.draw.rect(surface, check_color, inner_rect, border_radius=2)

    return rect

def draw_dropdown(surface, rect, options, selected_index, base_color, hover_color, text_color, font, border_color=WHITE, border_width=2, open=False):
    # Vykreslení hlavního tlačítka
    pygame.draw.rect(surface, base_color, rect, border_radius=5)
    if border_width > 0:
        pygame.draw.rect(surface, border_color, rect, border_width, border_radius=5)

    # Vykreslení textu a šipky
    selected_text = options[selected_index]
    draw_text(surface, selected_text, font, text_color, (rect.centerx - 10, rect.centery))

    # Šipka dolů/nahoru
    arrow_points = []
    if open:
        # Šipka nahoru
        arrow_points = [(rect.right - 20, rect.centery + 5),
                        (rect.right - 10, rect.centery - 5),
                        (rect.right - 30, rect.centery - 5)]
    else:
        # Šipka dolů
        arrow_points = [(rect.right - 20, rect.centery - 5),
                        (rect.right - 10, rect.centery + 5),
                        (rect.right - 30, rect.centery + 5)]

    pygame.draw.polygon(surface, text_color, arrow_points)

    # Pokud je rozbalovací seznam otevřený, vykreslit možnosti
    option_rects = []
    if open:
        for i, option in enumerate(options):
            option_rect = pygame.Rect(rect.x, rect.y + rect.height + i * rect.height, rect.width, rect.height)
            mouse_pos = pygame.mouse.get_pos()
            is_hovering = option_rect.collidepoint(mouse_pos)

            pygame.draw.rect(surface, hover_color if is_hovering else base_color, option_rect, border_radius=5)
            if border_width > 0:
                pygame.draw.rect(surface, border_color, option_rect, border_width, border_radius=5)

            draw_text(surface, option, font, text_color, option_rect.center)
            option_rects.append(option_rect)

    return rect, option_rects

def draw_tab_bar(surface, rect, tabs, current_tab, font, active_color, inactive_color, text_color):
    tab_width = rect.width // len(tabs)
    tab_rects = []

    for i, tab in enumerate(tabs):
        tab_rect = pygame.Rect(rect.x + i * tab_width, rect.y, tab_width, rect.height)
        is_active = i == current_tab

        pygame.draw.rect(surface, active_color if is_active else inactive_color, tab_rect, border_radius=5)
        if is_active:
            # Podtržení aktivní záložky
            underline_rect = pygame.Rect(tab_rect.x + 5, tab_rect.bottom - 3, tab_rect.width - 10, 3)
            pygame.draw.rect(surface, text_color, underline_rect, border_radius=2)

        draw_text(surface, tab, font, text_color, tab_rect.center)
        tab_rects.append(tab_rect)

    return tab_rects

# --- Funkce pro vykreslení splash screenu ---
def draw_splash_screen(surface, alpha):
    # Jednoduché černé pozadí
    surface.fill((0, 0, 0))
    
    # Původní logo hry
    font = pygame.font.Font(None, 120)
    text = font.render("LIGHT OR DEAD", True, (255, 255, 255))
    text_rect = text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 50))
    surface.blit(text, text_rect)
    
    # Podtitul
    subtitle_font = pygame.font.Font(None, 48)
    subtitle = subtitle_font.render("Survive the Darkness", True, (200, 200, 200))
    subtitle_rect = subtitle.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
    surface.blit(subtitle, subtitle_rect)
    
    # Výzva k pokračování
    prompt_font = pygame.font.Font(None, 36)
    prompt = prompt_font.render("Click to continue...", True, (180, 180, 180))
    prompt_rect = prompt.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT - 100))
    surface.blit(prompt, prompt_rect)

# --- Funkce pro vykreslení hlavního menu ---
def draw_main_menu(surface, high_score):
    # Create a dynamic background
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
    overlay.fill((0, 0, 0, 200))  # Darker background for better contrast
    
    # Animated background particles
    particle_count = 30
    for i in range(particle_count):
        x = (time.time() * 50 + i * 100) % SCREEN_WIDTH
        y = (time.time() * 30 + i * 50) % SCREEN_HEIGHT
        size = 2 + math.sin(time.time() + i) * 1
        alpha = int(100 + 155 * math.sin(time.time() * 2 + i))
        alpha = max(0, min(255, int(alpha)))
        color = (255, 255, 255, alpha)
        pygame.draw.circle(surface, color, (int(x), int(y)), int(size))
    
    # Enhanced logo with glow
    font_size = 96
    font = pygame.font.Font(None, font_size)
    text = font.render("LIGHT OR DEAD", True, (255, 255, 255))
    text_rect = text.get_rect(center=(SCREEN_WIDTH//2, 100))
    
    # Add text shadow for better readability
    shadow = font.render("LIGHT OR DEAD", True, (0, 0, 0))
    shadow_rect = shadow.get_rect(center=(SCREEN_WIDTH//2 + 2, 102))
    surface.blit(shadow, shadow_rect)
    surface.blit(text, text_rect)
    
    # Draw buttons with hover effects
    button_font = pygame.font.Font(None, 48)
    button_height = 60
    button_width = 300
    button_spacing = 20
    start_y = SCREEN_HEIGHT//2 - button_height
    
    # Start button
    start_rect = pygame.Rect((SCREEN_WIDTH - button_width)//2, start_y, button_width, button_height)
    start_hover = start_rect.collidepoint(pygame.mouse.get_pos())
    start_color = (100, 100, 100) if start_hover else (50, 50, 50)
    pygame.draw.rect(surface, start_color, start_rect)
    pygame.draw.rect(surface, (200, 200, 200), start_rect, 2)
    start_text = button_font.render("Start Game", True, (255, 255, 255))
    start_text_rect = start_text.get_rect(center=start_rect.center)
    surface.blit(start_text, start_text_rect)
    
    # Settings button
    settings_rect = pygame.Rect((SCREEN_WIDTH - button_width)//2, start_y + button_height + button_spacing, 
                              button_width, button_height)
    settings_hover = settings_rect.collidepoint(pygame.mouse.get_pos())
    settings_color = (100, 100, 100) if settings_hover else (50, 50, 50)
    pygame.draw.rect(surface, settings_color, settings_rect)
    pygame.draw.rect(surface, (200, 200, 200), settings_rect, 2)
    settings_text = button_font.render("Settings", True, (255, 255, 255))
    settings_text_rect = settings_text.get_rect(center=settings_rect.center)
    surface.blit(settings_text, settings_text_rect)
    
    # Quit button
    quit_rect = pygame.Rect((SCREEN_WIDTH - button_width)//2, 
                          start_y + (button_height + button_spacing) * 2,
                          button_width, button_height)
    quit_hover = quit_rect.collidepoint(pygame.mouse.get_pos())
    quit_color = (100, 100, 100) if quit_hover else (50, 50, 50)
    pygame.draw.rect(surface, quit_color, quit_rect)
    pygame.draw.rect(surface, (200, 200, 200), quit_rect, 2)
    quit_text = button_font.render("Quit", True, (255, 255, 255))
    quit_text_rect = quit_text.get_rect(center=quit_rect.center)
    surface.blit(quit_text, quit_text_rect)
    
    # High score display
    score_font = pygame.font.Font(None, 36)
    score_text = score_font.render(f"High Score: {high_score}", True, (200, 200, 200))
    score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT - 50))
    surface.blit(score_text, score_rect)
    
    # Apply the overlay
    surface.blit(overlay, (0, 0))
    
    return start_hover, settings_hover, quit_hover

def draw_shop_menu(surface, player_stats, weapons):
    # Create a semi-transparent background
    shop_bg = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
    shop_bg.fill((0, 0, 0, 200))
    surface.blit(shop_bg, (0, 0))

    # Shop title with glow effect
    title_text = "WEAPON SHOP"
    title_font = pygame.font.Font(None, 72)
    title_color = (255, 220, 150)

    # Glow effect for title
    for i in range(6):
        glow_alpha = 80 - i * 12
        glow_color = (*title_color, glow_alpha)
        offset = i * 2
        draw_text(surface, title_text, title_font, glow_color,
                 (SCREEN_WIDTH // 2 + offset, 80 + offset))

    # Main title
    draw_text(surface, title_text, title_font, title_color, (SCREEN_WIDTH // 2, 80))

    # Player stats display
    stats_bg = pygame.Surface((300, 100), pygame.SRCALPHA)
    stats_bg.fill((0, 0, 0, 150))
    surface.blit(stats_bg, (SCREEN_WIDTH - 320, 20))

    # Stats text
    stats_font = pygame.font.Font(None, 32)
    draw_text(surface, f"Score: {score}", stats_font, YELLOW, (SCREEN_WIDTH - 170, 40))
    draw_text(surface, f"Light: {int(player_stats['light_radius'])}", stats_font, WHITE, (SCREEN_WIDTH - 170, 80))

    # Shop grid layout
    grid_start_x = 50
    grid_start_y = 150
    item_width = 250
    item_height = 120
    items_per_row = 3
    spacing = 20

    # Vykreslení položek v obchodě
    for i, item in enumerate(shop_items):
        row = i // items_per_row
        col = i % items_per_row
        x = grid_start_x + col * (item_width + spacing)
        y = grid_start_y + row * (item_height + spacing)
        
        # Pozadí položky
        item_rect = pygame.Rect(x, y, item_width, item_height)
        pygame.draw.rect(surface, (30, 30, 40), item_rect)
        pygame.draw.rect(surface, WHITE, item_rect, 2)
        
        # Název položky
        name_font = pygame.font.Font(None, 24)
        name_text = name_font.render(item['name'], True, WHITE)
        name_rect = name_text.get_rect(center=(x + item_width//2, y + 30))
        surface.blit(name_text, name_rect)
        
        # Popis položky s počtem upgradů
        desc_font = pygame.font.Font(None, 20)
        if 'max_upgrades' in item:
            current = 0
            if item['id'] == 'firerate':
                current = int((player_stats['fire_rate_multiplier'] - 1.0) / 0.15)
            elif item['id'] == 'move_speed':
                current = int((player_stats['speed_multiplier'] - 1.0) / 0.15)
            elif item['id'] == 'bullet_dmg':
                current = player_stats['bullet_damage'] - 2  # Upraveno pro nový BASE_BULLET_DAMAGE
            elif item['id'] == 'light':
                current = int(player_stats['light_to_add'] / 50)
            desc_text = f"{item['desc'].split('(')[0]}({current}/{item['max_upgrades']})"
        else:
            desc_text = item['desc']
        desc_surface = desc_font.render(desc_text, True, WHITE)
        desc_rect = desc_surface.get_rect(center=(x + item_width//2, y + 60))
        surface.blit(desc_surface, desc_rect)
        
        # Cena
        cost_font = pygame.font.Font(None, 28)
        cost_text = cost_font.render(f"{item['cost']} pts", True, WHITE)
        cost_rect = cost_text.get_rect(center=(x + item_width//2, y + item_height - 40))
        surface.blit(cost_text, cost_rect)
        
        # Tlačítko pro nákup
        button_rect = pygame.Rect(x + 20, y + item_height - 80, item_width - 40, 30)
        pygame.draw.rect(surface, (0, 100, 0), button_rect)
        pygame.draw.rect(surface, WHITE, button_rect, 2)
        
        button_font = pygame.font.Font(None, 20)
        button_text = button_font.render("BUY", True, WHITE)
        button_text_rect = button_text.get_rect(center=button_rect.center)
        surface.blit(button_text, button_text_rect)
        
        # Kontrola, zda je položka dostupná
        if 'max_upgrades' in item:
            current = 0
            if item['id'] == 'firerate':
                current = int((player_stats['fire_rate_multiplier'] - 1.0) / 0.15)
            elif item['id'] == 'move_speed':
                current = int((player_stats['speed_multiplier'] - 1.0) / 0.15)
            elif item['id'] == 'bullet_dmg':
                current = player_stats['bullet_damage'] - 2  # Upraveno pro nový BASE_BULLET_DAMAGE
            elif item['id'] == 'light':
                current = int(player_stats['light_to_add'] / 50)
                
            if current >= item['max_upgrades']:
                # Překřížení položky, pokud je dosaženo maxima
                overlay = pygame.Surface((item_width, item_height), pygame.SRCALPHA)
                overlay.fill((0, 0, 0, 128))
                surface.blit(overlay, (x, y))
                max_text = button_font.render("MAX", True, WHITE)
                max_rect = max_text.get_rect(center=(x + item_width//2, y + item_height//2))
                surface.blit(max_text, max_rect)

    # Exit button
    exit_button_rect = pygame.Rect(SCREEN_WIDTH // 2 - 100, SCREEN_HEIGHT - 80, 200, 50)
    
    # Button background with gradient
    for y in range(50):
        color = (40, 40, 50, 200)
        pygame.draw.line(surface, color,
                        (exit_button_rect.left, exit_button_rect.top + y),
                        (exit_button_rect.right, exit_button_rect.top + y))

    # Button border
    pygame.draw.rect(surface, WHITE, exit_button_rect, 2, border_radius=10)

    # Button text
    exit_font = pygame.font.Font(None, 36)
    draw_text(surface, "Exit Shop", exit_font, WHITE, exit_button_rect.center)

    return shop_items, exit_button_rect

# --- Funkce pro vykreslení nastavení ---
def draw_settings_menu(surface, settings, current_tab, tabs):
    # Vyplnění pozadí tmavou barvou
    surface.fill(BLACK)

    # Nadpis
    title_text = "Settings"
    title_font = pygame.font.Font(None, 60)
    draw_text(surface, title_text, title_font, WHITE, (SCREEN_WIDTH // 2, 60))

    # Záložky
    tab_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 - 300, 120, 600, 40)
    tab_rects = draw_tab_bar(surface, tab_bar_rect, tabs, current_tab, pygame.font.Font(None, 30),
                            DARK_GREY, BLACK, WHITE)

    # Oblast obsahu
    content_rect = pygame.Rect(SCREEN_WIDTH // 2 - 350, 180, 700, 350)
    pygame.draw.rect(surface, DARK_GREY, content_rect, border_radius=5)
    pygame.draw.rect(surface, WHITE, content_rect, 2, border_radius=5)

    # Tlačítko zpět
    back_button_rect = pygame.Rect(SCREEN_WIDTH // 2 - 100, SCREEN_HEIGHT - 70, 200, 50)
    back_hover = draw_button(surface, "Back", pygame.font.Font(None, 36), BLACK, back_button_rect, RED, WHITE)

    # Obsah podle aktuální záložky
    if current_tab == 0:  # Graphics
        # Rozlišení
        draw_text(surface, "Resolution:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 50))
        resolution_options = ["800x600", "1024x768", "1280x720", "1920x1080"]
        resolution_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 35, 250, 30)
        resolution_dropdown, resolution_options_rects = draw_dropdown(surface, resolution_rect, resolution_options,
                                                                    0, DARK_GREY, GREY, WHITE,
                                                                    pygame.font.Font(None, 24))

        # Kvalita
        draw_text(surface, "Quality:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 100))
        quality_options = ["Low", "Medium", "High"]
        quality_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 85, 250, 30)
        quality_dropdown, quality_options_rects = draw_dropdown(surface, quality_rect, quality_options,
                                                              settings['quality'], DARK_GREY, GREY, WHITE,
                                                              pygame.font.Font(None, 24))

        # VSync
        draw_text(surface, "VSync:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 150))
        vsync_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 140, 30, 30)
        vsync_checkbox = draw_checkbox(surface, vsync_rect, settings['vsync'], DARK_GREY, GREEN)

        # FPS Counter
        draw_text(surface, "Show FPS:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 200))
        fps_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 190, 30, 30)
        fps_checkbox = draw_checkbox(surface, fps_rect, settings['show_fps'], DARK_GREY, GREEN)

    elif current_tab == 1:  # Audio
        # Hlavní hlasitost
        draw_text(surface, "Master Volume:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 50))
        volume_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 45, 250, 20)
        volume_slider = draw_slider(surface, volume_rect, settings['volume'], 0.0, 1.0, DARK_GREY, GREEN)

        # Hlasitost hudby
        draw_text(surface, "Music Volume:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 100))
        music_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 95, 250, 20)
        music_slider = draw_slider(surface, music_rect, settings['music_volume'], 0.0, 1.0, DARK_GREY, GREEN)

    elif current_tab == 2:  # Controls
        # Klávesy pro pohyb
        draw_text(surface, "Move Up:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 50))
        draw_text(surface, pygame.key.name(settings['keybinds']['move_up']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 50))

        draw_text(surface, "Move Down:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 90))
        draw_text(surface, pygame.key.name(settings['keybinds']['move_down']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 90))

        draw_text(surface, "Move Left:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 130))
        draw_text(surface, pygame.key.name(settings['keybinds']['move_left']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 130))

        draw_text(surface, "Move Right:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 170))
        draw_text(surface, pygame.key.name(settings['keybinds']['move_right']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 170))

        # Klávesy pro zbraně
        draw_text(surface, "Weapon 1:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 210))
        draw_text(surface, pygame.key.name(settings['keybinds']['weapon_1']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 210))

        draw_text(surface, "Weapon 2:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 250))
        draw_text(surface, pygame.key.name(settings['keybinds']['weapon_2']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 250))

        draw_text(surface, "Weapon 3:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 290))
        draw_text(surface, pygame.key.name(settings['keybinds']['weapon_3']).upper(), pygame.font.Font(None, 30), YELLOW, (content_rect.x + 350, content_rect.y + 290))

    elif current_tab == 3:  # Gameplay
        # Obtížnost
        draw_text(surface, "Difficulty:", pygame.font.Font(None, 30), WHITE, (content_rect.x + 150, content_rect.y + 50))
        difficulty_options = ["Easy", "Normal", "Hard"]
        difficulty_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 35, 250, 30)
        difficulty_dropdown, difficulty_options_rects = draw_dropdown(surface, difficulty_rect, difficulty_options,
                                                                    1, DARK_GREY, GREY, WHITE,
                                                                    pygame.font.Font(None, 24))

        # Další nastavení hry...

    return back_hover, tab_rects

# --- Spawn Minionů (Nová Funkce) ---
def spawn_minions(boss_x, boss_y, count, wave_num):
    global enemies, enemies_alive_in_wave
    print(f"Boss summoning {count} minions...")
    # Minioni mají snížené HP oproti běžným nepřátelům dané vlny
    minion_health = int((ENEMY_BASE_HEALTH + int(wave_num * 0.8)) * BOSS_SUMMON_HEALTH_FACTOR)
    minion_health = max(1, minion_health) # Alespoň 1 HP
    # Ostatní staty mohou být stejné jako u běžných nepřátel dané vlny nebo upravené
    minion_speed = ENEMY_BASE_SPEED + wave_num * 4.0 + (wave_num // 4) * 5.0
    minion_damage = ENEMY_BASE_DAMAGE + int(wave_num * 0.5)
    minion_score = int((ENEMY_BASE_SCORE + wave_num) * 0.5) # Menší skóre za miniony

    spawn_radius = BOSS_SIZE * 1.5 # Jak daleko od bosse se spawnují

    for _ in range(count):
        # Náhodný úhel a vzdálenost od bosse
        angle = random.uniform(0, 2 * math.pi)
        dist = random.uniform(BOSS_SIZE * 0.6, spawn_radius)
        ex = boss_x + math.cos(angle) * dist
        ey = boss_y + math.sin(angle) * dist

        # Omezení pozice na obrazovce (pro jistotu)
        ex = max(ENEMY_BASE_SIZE / 2, min(SCREEN_WIDTH - ENEMY_BASE_SIZE / 2, ex))
        ey = max(ENEMY_BASE_SIZE / 2, min(SCREEN_HEIGHT - ENEMY_BASE_SIZE / 2, ey))

        enemies.append({
            'x': float(ex), 'y': float(ey),
            'rect': pygame.Rect(ex - ENEMY_BASE_SIZE / 2, ey - ENEMY_BASE_SIZE / 2, ENEMY_BASE_SIZE, ENEMY_BASE_SIZE),
            'max_health': minion_health, 'health': minion_health,
            'speed': minion_speed, 'damage': minion_damage,
            'score': minion_score,
            'is_boss': False, # Není boss
            'is_minion': True, # Označení miniona
            'size': ENEMY_BASE_SIZE
        })
        enemies_alive_in_wave += 1 # Zvýšíme počet živých nepřátel

# --- Wave Management (Upraveno pro Boss Wave & Scaling) ---
def start_wave(wave_num):
    global current_wave, enemies_to_spawn_in_wave, enemies_alive_in_wave, wave_message_timer, wave_message_text, enemies, score, traps # Přidáno score pro škálování a traps
    current_wave = wave_num
    enemies.clear()
    boss_bullets.clear() # Vyčistit střely bosse z minulé vlny
    spawn_margin = 50

    # Generování pastí pro tuto vlnu
    num_traps = random.randint(1, 3 + current_wave // 2)  # Více pastí ve vyšších vlnách
    for _ in range(num_traps):
        if random.random() < TRAP_SPAWN_CHANCE:
            # Náhodná pozice na obrazovce (ne příliš blízko hráče)
            safe_distance = 150  # Minimální vzdálenost od hráče
            while True:
                trap_x = random.randint(50, SCREEN_WIDTH - 50)
                trap_y = random.randint(50, SCREEN_HEIGHT - 50)

                # Kontrola vzdálenosti od hráče
                dx = trap_x - player_stats['x']
                dy = trap_y - player_stats['y']
                dist = math.sqrt(dx*dx + dy*dy)

                if dist > safe_distance:
                    break

            # Náhodný typ pasti
            trap_type = random.randint(0, 2)  # 0=bodce, 1=jed, 2=zpomalení
            new_trap = create_trap(trap_x, trap_y, trap_type)
            if new_trap:
                traps.append(new_trap)
                print(f"Spawned trap type {trap_type} at ({trap_x}, {trap_y})")

    is_boss_wave = (wave_num > 0 and wave_num % BOSS_WAVE_INTERVAL == 0)

    if is_boss_wave:
        enemies_to_spawn_in_wave = 1
        enemies_alive_in_wave = 1
        wave_message_text = f"WAVE {wave_num} - BOSS WAVE!"

        base_health_this_wave = ENEMY_BASE_HEALTH + int(wave_num * 0.8)
        base_speed_this_wave = ENEMY_BASE_SPEED + wave_num * 4.0 + (wave_num // 4) * 5.0
        base_damage_this_wave = ENEMY_BASE_DAMAGE + int(wave_num * 0.5)
        base_score_this_wave = ENEMY_BASE_SCORE + wave_num

        # <<< Škálování HP bosse i podle skóre hráče >>>
        score_hp_bonus = int((score / 100) * BOSS_SCORE_HP_FACTOR) # Příklad: +1 HP za každých 1000 bodů score
        boss_health = int(base_health_this_wave * BOSS_HEALTH_MULTIPLIER) + score_hp_bonus
        boss_health = max(boss_health, base_health_this_wave * 5) # Zajistíme minimální HP
        # --- Konec škálování HP ---

        boss_speed = base_speed_this_wave * BOSS_SPEED_MULTIPLIER
        boss_damage = int(base_damage_this_wave * BOSS_DAMAGE_MULTIPLIER)
        boss_score = base_score_this_wave * BOSS_SCORE_MULTIPLIER

        print(f"Starting BOSS WAVE {wave_num}: HP:{boss_health} (Score Bonus: {score_hp_bonus}) S:{boss_speed:.1f} D:{boss_damage} Score:{boss_score}")

        ex, ey = SCREEN_WIDTH / 2, -spawn_margin # Spawn uprostřed nahoře

        enemies.append({
            'x': float(ex), 'y': float(ey),
            'rect': pygame.Rect(ex - BOSS_SIZE / 2, ey - BOSS_SIZE / 2, BOSS_SIZE, BOSS_SIZE),
            'max_health': boss_health, 'health': boss_health,
            'speed': boss_speed, 'damage': boss_damage, 'score': boss_score,
            'is_boss': True, 'size': BOSS_SIZE,
            # Stavy pro mechaniky bosse
            'action_cooldown': random.uniform(1.0, BOSS_ACTION_COOLDOWN_MIN), # První akce dříve
            'is_dashing': False, 'dash_timer': 0.0,
            'dash_target_x': 0.0, 'dash_target_y': 0.0
        })

    else: # Regular Wave
        enemies_to_spawn_in_wave = 5 + int(wave_num * 1.5 + (wave_num // (BOSS_WAVE_INTERVAL + 1)) * 3)
        wave_enemy_health = ENEMY_BASE_HEALTH + int(wave_num * 0.8)
        wave_enemy_speed = ENEMY_BASE_SPEED + wave_num * 4.0 + (wave_num // 4) * 5.0
        wave_enemy_damage = ENEMY_BASE_DAMAGE + int(wave_num * 0.5)
        wave_enemy_score = ENEMY_BASE_SCORE + wave_num
        print(f"Starting Wave {wave_num}: Spawning {enemies_to_spawn_in_wave} | H:{wave_enemy_health} S:{wave_enemy_speed:.1f} D:{wave_enemy_damage}")
        enemies_alive_in_wave = enemies_to_spawn_in_wave
        wave_message_text = f"Wave {current_wave}"

        for _ in range(enemies_to_spawn_in_wave):
            side = random.choice(['top','bottom','left','right'])
            if side=='top': ex,ey=random.uniform(0,SCREEN_WIDTH),-spawn_margin
            elif side=='bottom': ex,ey=random.uniform(0,SCREEN_WIDTH),SCREEN_HEIGHT+spawn_margin
            elif side=='left': ex,ey=-spawn_margin,random.uniform(0,SCREEN_HEIGHT)
            else: ex,ey=SCREEN_WIDTH+spawn_margin,random.uniform(0,SCREEN_HEIGHT)
            enemies.append({
                'x':float(ex), 'y':float(ey),
                'rect':pygame.Rect(ex - ENEMY_BASE_SIZE / 2, ey - ENEMY_BASE_SIZE / 2, ENEMY_BASE_SIZE, ENEMY_BASE_SIZE),
                'max_health':wave_enemy_health, 'health':wave_enemy_health,
                'speed':wave_enemy_speed, 'damage':wave_enemy_damage,
                'score':wave_enemy_score,
                'is_boss': False, 'is_minion': False, # Označení běžného nepřítele
                'size': ENEMY_BASE_SIZE
            })

    wave_message_timer = WAVE_MESSAGE_DURATION

# --- Funkce pro reset hry (Upraveno pro zbraně, pasti a výzvy) ---
def reset_game():
    global player_stats, score, high_score, enemies, bullets, boss_bullets, last_shot_time, game_state, current_wave, enemies_alive_in_wave, wave_transition_timer, wave_message_timer, wave_message_text, weapons, traps, challenges

    # Reset zbraní - pouze pistole je vlastněná na začátku
    weapons[WEAPON_PISTOL]['owned'] = True
    weapons[WEAPON_SHOTGUN]['owned'] = False
    weapons[WEAPON_MACHINEGUN]['owned'] = False

    # Reset pastí
    traps.clear()

    # Reset výzev
    for challenge in challenges:
        challenge['completed'] = False
        if 'kills_current' in challenge:
            challenge['kills_current'] = 0
        if 'traps_current' in challenge:
            challenge['traps_current'] = 0

    player_stats = {
        'x': float(PLAYER_START_X),
        'y': float(PLAYER_START_Y),
        'health': PLAYER_MAX_HEALTH,
        'max_health': PLAYER_MAX_HEALTH,
        'light_radius': INITIAL_LIGHT_RADIUS,
        'speed_multiplier': 1.0,
        'fire_rate_multiplier': 1.0,
        'bullet_damage': BASE_BULLET_DAMAGE,
        'light_to_add': 0.0,
        'current_weapon': WEAPON_PISTOL,  # Začínáme s pistolí
        'weapons': [0, 1, 2],  # Indexy dostupných zbraní
        'poisoned': False,     # Zda je hráč otráven
        'poison_timer': 0.0,   # Zbývající čas otravy
        'slowed': False,       # Zda je hráč zpomalen
        'slow_timer': 0.0,     # Zbývající čas zpomalení
        'challenges_completed': 0  # Počet splněných výzev
    }

    score = 0
    high_score = load_highscore()
    enemies.clear()
    bullets.clear()
    boss_bullets.clear() # Vyčistit i střely bosse
    last_shot_time = 0
    current_wave = 0
    enemies_alive_in_wave = 0
    wave_transition_timer = 0.0 # Začínáme hned
    wave_message_timer = WAVE_MESSAGE_DURATION
    wave_message_text = "Get Ready!"
    game_state = STATE_PLAYING
    print("Game reset and started.")

# --- Performance Monitoring ---
class PerformanceMonitor:
    def __init__(self, window_size=60):  # 60 frames window for averaging
        self.frame_times = []
        self.window_size = window_size
        self.last_time = time.time()
        self.fps_history = []
        self.current_fps = 0

    def update(self):
        current_time = time.time()
        dt = current_time - self.last_time
        self.last_time = current_time

        self.frame_times.append(dt)
        if len(self.frame_times) > self.window_size:
            self.frame_times.pop(0)

        if len(self.frame_times) > 0:
            avg_frame_time = sum(self.frame_times) / len(self.frame_times)
            self.current_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
            self.fps_history.append(self.current_fps)
            if len(self.fps_history) > 10:  # Keep only last 10 FPS readings
                self.fps_history.pop(0)

    def get_fps(self):
        return self.current_fps

    def get_avg_fps(self):
        if len(self.fps_history) > 0:
            return sum(self.fps_history) / len(self.fps_history)
        return 0

# --- Herní smyčka ---
running = True; current_timestamp = time.time()
glEnable(GL_BLEND); glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
print("--- Start main loop ---")

# Inicializace performance monitoru
perf_monitor = PerformanceMonitor()
show_performance = settings['show_fps']  # Přepínač pro zobrazení výkonu

# Inicializace splash screenu
splash_start_time = time.time()
splash_alpha = 0

# Proměnné pro dropdown menu
dropdown_open = False
selected_dropdown = None

while running:
    last_frame_timestamp = current_timestamp; current_timestamp = time.time()
    dt = current_timestamp - last_frame_timestamp; dt = min(dt, 0.1)
    mouse_pos = pygame.mouse.get_pos(); mouse_pressed = pygame.mouse.get_pressed(); mouse_click = False

    events = pygame.event.get()
    for event in events:
        if event.type == pygame.QUIT: running = False
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1: mouse_click = True
        if event.type == pygame.KEYDOWN:
            # Splash screen - jakákoliv klávesa pokračuje
            if game_state == STATE_SPLASH:
                game_state = STATE_MAIN_MENU
                continue

            # Escape klávesa
            if event.key == pygame.K_ESCAPE:
                if game_state == STATE_MAIN_MENU:
                    running = False
                elif game_state == STATE_SETTINGS:
                    game_state = STATE_MAIN_MENU
                elif game_state == STATE_SHOP:
                    game_state = STATE_PLAYING
                elif game_state == STATE_GAME_OVER:
                    running = False

            # Herní klávesy
            if game_state == STATE_PLAYING:
                if event.key == pygame.K_p:
                    game_state = STATE_SHOP

                # Přepínání zbraní
                if event.key == settings['keybinds']['weapon_1'] and weapons[0]['owned']:
                    player_stats['current_weapon'] = WEAPON_PISTOL
                    print("Switched to Pistol")
                elif event.key == settings['keybinds']['weapon_2'] and weapons[1]['owned']:
                    player_stats['current_weapon'] = WEAPON_SHOTGUN
                    print("Switched to Shotgun")
                elif event.key == settings['keybinds']['weapon_3'] and weapons[2]['owned']:
                    player_stats['current_weapon'] = WEAPON_MACHINEGUN
                    print("Switched to Machine Gun")

            # Restart hry
            if game_state == STATE_GAME_OVER and event.key == pygame.K_r:
                reset_game()

            # Přepínání zobrazení výkonu pomocí F3
            if event.key == pygame.K_F3:
                settings['show_fps'] = not settings['show_fps']
                show_performance = settings['show_fps']
                print(f"Performance display: {'ON' if show_performance else 'OFF'}")

            # Přepínání kvality pomocí F2
            if event.key == pygame.K_F2:
                settings['quality'] = (settings['quality'] + 1) % 3  # Cykluje mezi 0, 1, 2
                quality_names = ["Low", "Medium", "High"]
                print(f"Quality set to: {quality_names[settings['quality']]}")

                # Upravit nastavení podle kvality
                if settings['quality'] == 0:  # Low
                    RENDER_OPTIMIZATION = True
                    RENDER_INTERVAL = 1.0 / 30.0  # 30 FPS target
                elif settings['quality'] == 1:  # Medium
                    RENDER_OPTIMIZATION = True
                    RENDER_INTERVAL = 1.0 / 60.0  # 60 FPS target
                else:  # High
                    RENDER_OPTIMIZATION = False

    # --- Logika podle stavu hry ---
    if game_state == STATE_SPLASH:
        # Fade-in efekt
        elapsed = current_timestamp - splash_start_time

        # Rychlejší fade-in
        if elapsed < SPLASH_DURATION * 0.2:  # První 20% času - fade in
            splash_alpha = int(255 * (elapsed / (SPLASH_DURATION * 0.2)))
        else:  # Zbytek času - plná viditelnost
            splash_alpha = 255

        # Přechod do menu po kliknutí
        if mouse_click:
            # Efekt fade-out při kliknutí
            fade_out_surface = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            fade_out_surface.fill(BLACK)
            for alpha in range(0, 256, 25):  # Rychlý fade-out
                fade_out_surface.set_alpha(alpha)
                draw_surface.blit(fade_out_surface, (0, 0))
                pygame.display.flip()
                pygame.time.delay(20)  # Krátká pauza pro viditelný efekt

            game_state = STATE_MAIN_MENU

    elif game_state == STATE_MAIN_MENU:
        # Zpracování kliknutí na tlačítka v menu
        if mouse_click:
            start_hover, settings_hover, quit_hover = draw_main_menu(draw_surface, high_score)
            if start_hover:
                reset_game()
            elif settings_hover:
                game_state = STATE_SETTINGS
            elif quit_hover:
                running = False

    elif game_state == STATE_SETTINGS:
        # Zpracování kliknutí v nastavení
        back_hover, tab_rects = draw_settings_menu(draw_surface, settings, current_settings_tab, settings_tabs)

        # Definice oblasti obsahu pro nastavení
        content_rect = pygame.Rect(SCREEN_WIDTH // 2 - 350, 180, 700, 350)

        if mouse_click:
            # Kontrola kliknutí na záložky
            for i, tab_rect in enumerate(tab_rects):
                if tab_rect.collidepoint(mouse_pos):
                    current_settings_tab = i
                    break

            # Kontrola kliknutí na tlačítko zpět
            if back_hover:
                game_state = STATE_MAIN_MENU

            # Zpracování kliknutí na prvky v záložce Graphics
            if current_settings_tab == 0:
                # Kontrola kliknutí na checkbox VSync
                vsync_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 140, 30, 30)
                if vsync_rect.collidepoint(mouse_pos):
                    settings['vsync'] = not settings['vsync']
                    print(f"VSync: {settings['vsync']}")

                # Kontrola kliknutí na checkbox FPS Counter
                fps_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 190, 30, 30)
                if fps_rect.collidepoint(mouse_pos):
                    settings['show_fps'] = not settings['show_fps']
                    show_performance = settings['show_fps']
                    print(f"Show FPS: {settings['show_fps']}")

                # Kontrola kliknutí na dropdown Quality
                quality_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 85, 250, 30)
                if quality_rect.collidepoint(mouse_pos):
                    # Cyklické přepínání kvality
                    settings['quality'] = (settings['quality'] + 1) % 3
                    quality_names = ["Low", "Medium", "High"]
                    print(f"Quality set to: {quality_names[settings['quality']]}")

                    # Upravit nastavení podle kvality
                    if settings['quality'] == 0:  # Low
                        RENDER_OPTIMIZATION = True
                        RENDER_INTERVAL = 1.0 / 30.0  # 30 FPS target
                    elif settings['quality'] == 1:  # Medium
                        RENDER_OPTIMIZATION = True
                        RENDER_INTERVAL = 1.0 / 60.0  # 60 FPS target
                    else:  # High
                        RENDER_OPTIMIZATION = False

            # Zpracování kliknutí na prvky v záložce Audio
            elif current_settings_tab == 1:
                # Kontrola kliknutí na slidery hlasitosti
                volume_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 45, 250, 20)
                if volume_rect.collidepoint(mouse_pos):
                    # Nastavení hlasitosti podle pozice kliknutí
                    rel_x = (mouse_pos[0] - volume_rect.x) / volume_rect.width
                    settings['volume'] = max(0.0, min(1.0, rel_x))
                    print(f"Master Volume: {settings['volume']:.2f}")

                music_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 95, 250, 20)
                if music_rect.collidepoint(mouse_pos):
                    # Nastavení hlasitosti hudby podle pozice kliknutí
                    rel_x = (mouse_pos[0] - music_rect.x) / music_rect.width
                    settings['music_volume'] = max(0.0, min(1.0, rel_x))
                    print(f"Music Volume: {settings['music_volume']:.2f}")

            # Zpracování kliknutí na prvky v záložce Gameplay
            elif current_settings_tab == 3:
                # Kontrola kliknutí na dropdown Difficulty
                difficulty_rect = pygame.Rect(content_rect.x + 350, content_rect.y + 35, 250, 30)
                if difficulty_rect.collidepoint(mouse_pos):
                    # Cyklické přepínání obtížnosti
                    settings['difficulty'] = (settings['difficulty'] + 1) % 3
                    difficulty_names = ["Easy", "Normal", "Hard"]
                    print(f"Difficulty set to: {difficulty_names[settings['difficulty']]}")
    elif game_state == STATE_PLAYING:

        # Správa vln (beze změny)
        if wave_transition_timer > 0:
            wave_transition_timer -= dt
            if wave_transition_timer <= 0: start_wave(current_wave + 1)
        else:
            if enemies_alive_in_wave <= 0:
                if current_wave == 0: print("Starting Wave 1..."); start_wave(1)
                elif wave_transition_timer <= 0:
                    is_prev_wave_boss = (current_wave > 0 and current_wave % BOSS_WAVE_INTERVAL == 0)
                    print(f"Wave {current_wave} {'(Boss) ' if is_prev_wave_boss else ''}Cleared! Starting delay...")
                    wave_message_text = "BOSS DEFEATED!" if is_prev_wave_boss else f"Wave {current_wave} Cleared!"
                    wave_message_timer = WAVE_MESSAGE_DURATION
                    wave_transition_timer = WAVE_TRANSITION_DELAY
        if wave_message_timer > 0: wave_message_timer -= dt

        # Pohyb hráče (s podporou zpomalení)
        keys=pygame.key.get_pressed();move_x,move_y=0.0,0.0
        if keys[pygame.K_LEFT] or keys[pygame.K_a]:move_x-=1;
        if keys[pygame.K_RIGHT] or keys[pygame.K_d]:move_x+=1
        if keys[pygame.K_UP] or keys[pygame.K_w]:move_y-=1;
        if keys[pygame.K_DOWN] or keys[pygame.K_s]:move_y+=1
        move_len=math.hypot(move_x,move_y)

        # Aplikace zpomalení, pokud je hráč zpomalen
        current_player_speed = PLAYER_BASE_SPEED * player_stats['speed_multiplier']
        if player_stats['slowed']:
            current_player_speed *= TRAP_SLOW_FACTOR
            player_stats['slow_timer'] -= dt
            if player_stats['slow_timer'] <= 0:
                player_stats['slowed'] = False
                print("Slow effect ended")

        # Aplikace poškození jedem, pokud je hráč otráven
        if player_stats['poisoned']:
            player_stats['poison_timer'] -= dt
            # Poškození jedem každou sekundu
            if int(player_stats['poison_timer']) < int(player_stats['poison_timer'] + dt):
                player_stats['health'] -= TRAP_POISON_DAMAGE
                print(f"Poison damage! Health: {player_stats['health']}")

            if player_stats['poison_timer'] <= 0:
                player_stats['poisoned'] = False
                print("Poison effect ended")

        if move_len>0:move_x=(move_x/move_len)*current_player_speed*dt;move_y=(move_y/move_len)*current_player_speed*dt
        new_x=player_stats['x']+move_x;new_y=player_stats['y']+move_y;half_size=PLAYER_SIZE/2.0
        player_stats['x']=max(half_size,min(SCREEN_WIDTH-half_size,new_x));player_stats['y']=max(half_size,min(SCREEN_HEIGHT-half_size,new_y))

        # Detekce kolizí s pastmi
        player_rect = pygame.Rect(player_stats['x'] - PLAYER_SIZE/2, player_stats['y'] - PLAYER_SIZE/2, PLAYER_SIZE, PLAYER_SIZE)
        traps_to_remove = []

        for i, trap in enumerate(traps):
            if not trap['triggered'] and player_rect.colliderect(trap['rect']):
                # Aktivace pasti
                trap['triggered'] = True

                if trap['type'] == TRAP_TYPE_SPIKE:
                    # Bodce - okamžité poškození
                    player_stats['health'] -= trap['damage']
                    print(f"Stepped on spike trap! Damage: {trap['damage']}, Health: {player_stats['health']}")
                    traps_to_remove.append(i)

                elif trap['type'] == TRAP_TYPE_POISON:
                    # Jed - poškození za čas
                    player_stats['poisoned'] = True
                    player_stats['poison_timer'] = trap['duration']
                    print(f"Stepped on poison trap! Duration: {trap['duration']}s")
                    traps_to_remove.append(i)

                elif trap['type'] == TRAP_TYPE_SLOW:
                    # Zpomalení - snížení rychlosti
                    player_stats['slowed'] = True
                    player_stats['slow_timer'] = trap['duration']
                    print(f"Stepped on slow trap! Duration: {trap['duration']}s")
                    traps_to_remove.append(i)

                # Aktualizace výzvy pro vyhýbání se pastem
                for challenge in challenges:
                    if 'traps_required' in challenge and not challenge['completed']:
                        challenge['traps_current'] += 1
                        if challenge['traps_current'] >= challenge['traps_required']:
                            challenge['completed'] = True
                            score += challenge['reward']
                            player_stats['challenges_completed'] += 1
                            print(f"Challenge completed: {challenge['name']}! Reward: {challenge['reward']} points")

        # Odstranění aktivovaných pastí
        for i in sorted(traps_to_remove, reverse=True):
            if i < len(traps):
                traps.pop(i)

        # Střelba Hráče (Podpora různých zbraní)
        current_weapon = weapons[player_stats['current_weapon']]
        current_bullet_cooldown = BASE_BULLET_COOLDOWN / (player_stats['fire_rate_multiplier'] * current_weapon['fire_rate'])

        if mouse_pressed[0] and current_timestamp >= last_shot_time + current_bullet_cooldown:
            last_shot_time = current_timestamp

            # Použití směru zbraně pro střelbu
            dx = mouse_pos[0] - player_stats['x']
            dy = mouse_pos[1] - player_stats['y']
            weapon_angle = math.atan2(dy, dx)
            dist = math.hypot(dx, dy)

            if dist > 0:
                dir_x, dir_y = dx/dist, dy/dist

                # Efekt záblesku při střelbě
                flash_size = current_weapon['bullet_size'] * 2
                flash_x = player_stats.get('weapon_muzzle_x', player_stats['x'] + dir_x * PLAYER_SIZE)
                flash_y = player_stats.get('weapon_muzzle_y', player_stats['y'] + dir_y * PLAYER_SIZE)

                # Přidání záblesku jako krátkodobého efektu
                flash_color = (255, 255, 200, 200)  # Žlutobílá barva s průhledností
                flash_surface = pygame.Surface((flash_size*2, flash_size*2), pygame.SRCALPHA)
                pygame.draw.circle(flash_surface, flash_color, (flash_size, flash_size), flash_size)
                draw_surface.blit(flash_surface, (flash_x - flash_size, flash_y - flash_size))

                # Vytvoření střel podle typu zbraně
                for i in range(current_weapon['bullet_count']):
                    # Výpočet rozptylu pro více střel
                    spread_angle = 0
                    if current_weapon['bullet_count'] > 1:
                        # Rozptyl pro brokovnici - rovnoměrně rozložené střely v širokém úhlu
                        # Pro 7 střel: -45, -30, -15, 0, 15, 30, 45 stupňů
                        spread_angle = current_weapon['spread'] * (i - (current_weapon['bullet_count'] - 1) / 2)
                    elif current_weapon['spread'] > 0:
                        # Náhodný rozptyl pro jednu střelu (kulomet)
                        spread_angle = random.uniform(-current_weapon['spread'], current_weapon['spread'])

                    # Aplikace rozptylu na směr střely
                    spread_rad = math.radians(spread_angle)
                    spread_dir_x = dir_x * math.cos(spread_rad) - dir_y * math.sin(spread_rad)
                    spread_dir_y = dir_x * math.sin(spread_rad) + dir_y * math.cos(spread_rad)

                    # Použití pozice konce hlavně zbraně jako výchozí bod pro střely
                    bullet_start_x = player_stats.get('weapon_muzzle_x', player_stats['x'] + spread_dir_x * PLAYER_SIZE)
                    bullet_start_y = player_stats.get('weapon_muzzle_y', player_stats['y'] + spread_dir_y * PLAYER_SIZE)

                    # Použití object poolu pro střely
                    if OBJECT_POOLING:
                        bullet = bullet_pool.get()
                        bullet['x'] = bullet_start_x
                        bullet['y'] = bullet_start_y
                        bullet['vx'] = spread_dir_x * current_weapon['bullet_speed']
                        bullet['vy'] = spread_dir_y * current_weapon['bullet_speed']
                        bullet['rect'].width = current_weapon['bullet_size']
                        bullet['rect'].height = current_weapon['bullet_size']
                        bullet['rect'].center = (int(bullet['x']), int(bullet['y']))
                        bullet['damage'] = player_stats['bullet_damage'] * current_weapon['damage']
                        bullet['color'] = current_weapon['color']
                        bullets.append(bullet)
                    else:
                        bullets.append({
                            'x': bullet_start_x,
                            'y': bullet_start_y,
                            'vx': spread_dir_x * current_weapon['bullet_speed'],
                            'vy': spread_dir_y * current_weapon['bullet_speed'],
                            'rect': pygame.Rect(0, 0, current_weapon['bullet_size'], current_weapon['bullet_size']),
                            'damage': player_stats['bullet_damage'] * current_weapon['damage'],
                            'color': current_weapon['color']
                        })

        # Aktualizace spatial hash gridu pro nepřátele
        if ENABLE_SPATIAL_HASH:
            spatial_grid.clear()
            for j, enemy in enumerate(enemies):
                if enemy['health'] > 0:
                    spatial_grid.insert(enemy, j)

        # Aktualizace střel Hráče (Optimalizováno)
        bullets_to_remove_indices=[]
        enemies_hit_this_frame=set()

        for i, bullet in enumerate(bullets):
            bullet['x']+=bullet['vx']*dt
            bullet['y']+=bullet['vy']*dt
            bullet['rect'].center=(int(bullet['x']),int(bullet['y']))

            # Kontrola hranic obrazovky
            if not(0<bullet['rect'].centerx<SCREEN_WIDTH and 0<bullet['rect'].centery<SCREEN_HEIGHT):
                bullets_to_remove_indices.append(i)
                continue

            hit_enemy_this_bullet=False

            # Optimalizovaná detekce kolizí pomocí spatial hash
            if ENABLE_SPATIAL_HASH:
                nearby_enemies = spatial_grid.query_nearby(bullet['x'], bullet['y'], radius=1)
                for j in nearby_enemies:
                    enemy = enemies[j]
                    if j in enemies_hit_this_frame or enemy['health']<=0:
                        continue
                    if bullet['rect'].colliderect(enemy['rect']):
                        enemy['health']-=bullet['damage']
                        hit_enemy_this_bullet=True
                        enemies_hit_this_frame.add(j)
                        break
            else:
                # Původní detekce kolizí
                for j, enemy in enumerate(enemies):
                    if j in enemies_hit_this_frame or enemy['health']<=0:
                        continue
                    if bullet['rect'].colliderect(enemy['rect']):
                        enemy['health']-=bullet['damage']
                        hit_enemy_this_bullet=True
                        enemies_hit_this_frame.add(j)
                        break

            if hit_enemy_this_bullet:
                bullets_to_remove_indices.append(i)

        # Vrácení střel do poolu a odstranění ze seznamu
        for i in sorted(bullets_to_remove_indices, reverse=True):
            if i < len(bullets):
                if OBJECT_POOLING:
                    bullet_pool.release(bullets[i])
                bullets.pop(i)

        # <<< Aktualizace střel Bosse a nepřátel (Optimalizováno) >>>
        boss_bullets_to_remove = []
        enemy_bullets_to_remove = []
        player_rect_for_collision = pygame.Rect(player_stats['x'] - PLAYER_SIZE / 2, player_stats['y'] - PLAYER_SIZE / 2, PLAYER_SIZE, PLAYER_SIZE)

        # Aktualizace střel bosse
        for i, b_bullet in enumerate(boss_bullets):
            b_bullet['x'] += b_bullet['vx'] * dt
            b_bullet['y'] += b_bullet['vy'] * dt
            b_bullet['rect'].center = (int(b_bullet['x']), int(b_bullet['y']))

            # Optimalizace: Rychlá kontrola vzdálenosti před přesnou kolizí
            dx = player_stats['x'] - b_bullet['x']
            dy = player_stats['y'] - b_bullet['y']
            dist_squared = dx*dx + dy*dy
            collision_threshold = (PLAYER_SIZE/2 + BOSS_PROJECTILE_SIZE/2) ** 2

            # Kontrola hranic
            if not (0 < b_bullet['rect'].centerx < SCREEN_WIDTH and 0 < b_bullet['rect'].centery < SCREEN_HEIGHT):
                boss_bullets_to_remove.append(i)
                continue

            # Optimalizovaná kontrola kolize s hráčem
            if dist_squared < collision_threshold * 2:  # Širší kontrola pro jistotu
                if b_bullet['rect'].colliderect(player_rect_for_collision):
                    player_stats['health'] -= b_bullet['damage']
                    boss_bullets_to_remove.append(i)
                    # Zde by mohl být efekt zásahu hráče

        # Aktualizace střel nepřátel
        for i, e_bullet in enumerate(enemy_bullets):
            e_bullet['x'] += e_bullet['vx'] * dt
            e_bullet['y'] += e_bullet['vy'] * dt
            e_bullet['rect'].center = (int(e_bullet['x']), int(e_bullet['y']))

            # Kontrola hranic
            if not (0 < e_bullet['rect'].centerx < SCREEN_WIDTH and 0 < e_bullet['rect'].centery < SCREEN_HEIGHT):
                enemy_bullets_to_remove.append(i)
                continue

            # Kontrola kolize s hráčem
            if e_bullet['rect'].colliderect(player_rect_for_collision):
                player_stats['health'] -= e_bullet['damage']
                enemy_bullets_to_remove.append(i)

        # Odstranění střel bosse a vrácení do poolu
        for i in sorted(boss_bullets_to_remove, reverse=True):
            if i < len(boss_bullets):
                if OBJECT_POOLING:
                    boss_bullet_pool.release(boss_bullets[i])
                boss_bullets.pop(i)

        # Odstranění střel nepřátel
        for i in sorted(enemy_bullets_to_remove, reverse=True):
            if i < len(enemy_bullets):
                enemy_bullets.pop(i)

        # Aktualizace světla (Shrink + Grow Animation) (beze změny)
        add_this_frame=0.0
        if player_stats['light_to_add']>0.001: portion_to_add=player_stats['light_to_add']*LIGHT_GROW_INTERP_FACTOR*dt; add_this_frame=min(portion_to_add,player_stats['light_to_add']); player_stats['light_to_add']-=add_this_frame
        current_light_radius=player_stats['light_radius']; shrunk_radius=current_light_radius-LIGHT_SHRINK_RATE*dt; new_radius=shrunk_radius+add_this_frame
        player_stats['light_radius']=min(MAX_LIGHT_RADIUS,new_radius)

        # Kontrola smrti světlem (beze změny)
        if player_stats['light_radius']<=LIGHT_DEATH_THRESHOLD: player_stats['light_radius']=LIGHT_DEATH_THRESHOLD; player_stats['health']=0; print(f"Player light critical ({LIGHT_DEATH_THRESHOLD})!"); game_state=STATE_GAME_OVER

        # Pohyb a kolize nepřátel / Bosse (Optimalizováno)
        enemies_to_remove_indices = []

        # Předpočítat čtverec vzdálenosti pro kolize (rychlejší než sqrt)
        player_collision_radius_squared = (PLAYER_SIZE/2) ** 2

        for i, enemy in enumerate(enemies):
            # Zabití a odměny
            if enemy['health'] <= 0:
                if i not in enemies_to_remove_indices:
                    enemies_to_remove_indices.append(i)
                    score += enemy['score']
                    enemies_alive_in_wave -= 1

                    # Aktualizace výzvy pro zabití nepřátel
                    for challenge in challenges:
                        # Základní výzva pro zabití nepřátel
                        if 'kills_required' in challenge and not challenge['completed']:
                            # Kontrola, zda jde o výzvu pro konkrétní zbraň
                            if 'weapon_type' in challenge:
                                # Počítáme jen zabití danou zbraní
                                if player_stats['current_weapon'] == challenge['weapon_type']:
                                    challenge['kills_current'] += 1
                            else:
                                # Běžná výzva pro zabití - počítáme všechna zabití
                                challenge['kills_current'] += 1

                            # Kontrola splnění výzvy
                            if challenge['kills_current'] >= challenge['kills_required']:
                                challenge['completed'] = True
                                score += challenge['reward']
                                player_stats['challenges_completed'] += 1
                                print(f"Challenge completed: {challenge['name']}! Reward: {challenge['reward']} points")

                        # Výzva pro zabití bossů
                        if 'boss_kills_required' in challenge and not challenge['completed'] and enemy.get('is_boss', False):
                            challenge['boss_kills_current'] += 1
                            if challenge['boss_kills_current'] >= challenge['boss_kills_required']:
                                challenge['completed'] = True
                                score += challenge['reward']
                                player_stats['challenges_completed'] += 1
                                print(f"Challenge completed: {challenge['name']}! Reward: {challenge['reward']} points")

                    # Kontrola výzvy pro přežití vln
                    for challenge in challenges:
                        if 'check' in challenge and not challenge['completed']:
                            if challenge['check'](player_stats, current_wave):
                                challenge['completed'] = True
                                score += challenge['reward']
                                player_stats['challenges_completed'] += 1
                                print(f"Challenge completed: {challenge['name']}! Reward: {challenge['reward']} points")

                    if enemy.get('is_boss', False):
                        print("BOSS DEFEATED! Applying rewards.")
                        player_stats['health'] = player_stats['max_health']
                        player_stats['light_to_add'] += BOSS_LIGHT_REWARD
                    elif not enemy.get('is_minion', False): # Minioni nedávají světlo
                        player_stats['light_to_add'] += LIGHT_GAIN_PER_KILL
                continue

            # <<< Logika Bosse >>>
            if enemy.get('is_boss', False):
                enemy['action_cooldown'] -= dt

                # --- Dash Mechanika ---
                if enemy['is_dashing']:
                    # Pohyb během dashe
                    dash_dx = enemy['dash_target_x'] - enemy['x']
                    dash_dy = enemy['dash_target_y'] - enemy['y']
                    dist_to_target = math.hypot(dash_dx, dash_dy)
                    dash_speed_frame = BOSS_DASH_SPEED * dt

                    if dist_to_target <= dash_speed_frame: # Dosáhl cíle (nebo ho přestřelil)
                        enemy['x'] = enemy['dash_target_x']
                        enemy['y'] = enemy['dash_target_y']
                        enemy['is_dashing'] = False
                    else:
                        enemy['x'] += (dash_dx / dist_to_target) * dash_speed_frame
                        enemy['y'] += (dash_dy / dist_to_target) * dash_speed_frame

                    enemy['dash_timer'] -= dt
                    if enemy['dash_timer'] <= 0: # Časovač dashe vypršel
                        enemy['is_dashing'] = False

                # --- Výběr a provedení akce ---
                elif enemy['action_cooldown'] <= 0:
                    action = random.choice(['shoot', 'dash', 'summon', 'move']) # Přidána možnost 'move' pro pauzu
                    print(f"Boss action: {action}")

                    if action == 'shoot':
                        # Střelba na hráče (Optimalizováno)
                        dx = player_stats['x'] - enemy['x']
                        dy = player_stats['y'] - enemy['y']
                        dist = math.hypot(dx, dy)
                        if dist > 0:
                            dir_x, dir_y = dx / dist, dy / dist

                            # Použití object poolu pro střely bosse
                            if OBJECT_POOLING:
                                b_bullet = boss_bullet_pool.get()
                                b_bullet['x'] = enemy['x'] + dir_x * (BOSS_SIZE / 2)
                                b_bullet['y'] = enemy['y'] + dir_y * (BOSS_SIZE / 2)
                                b_bullet['vx'] = dir_x * BOSS_PROJECTILE_SPEED
                                b_bullet['vy'] = dir_y * BOSS_PROJECTILE_SPEED
                                b_bullet['rect'].center = (int(b_bullet['x']), int(b_bullet['y']))
                                b_bullet['damage'] = BOSS_PROJECTILE_DAMAGE
                                boss_bullets.append(b_bullet)
                            else:
                                boss_bullets.append({
                                    'x': enemy['x'] + dir_x * (BOSS_SIZE / 2),
                                    'y': enemy['y'] + dir_y * (BOSS_SIZE / 2),
                                    'vx': dir_x * BOSS_PROJECTILE_SPEED,
                                    'vy': dir_y * BOSS_PROJECTILE_SPEED,
                                    'rect': pygame.Rect(0,0, BOSS_PROJECTILE_SIZE, BOSS_PROJECTILE_SIZE),
                                    'damage': BOSS_PROJECTILE_DAMAGE
                                })
                        enemy['action_cooldown'] = random.uniform(BOSS_ACTION_COOLDOWN_MIN * 0.5, BOSS_ACTION_COOLDOWN_MAX * 0.8) # Kratší cooldown po střelbě

                    elif action == 'dash' and not enemy['is_dashing']:
                        # Výpad směrem k hráči
                        dx = player_stats['x'] - enemy['x']
                        dy = player_stats['y'] - enemy['y']
                        dist = math.hypot(dx, dy)
                        if dist > 20: # Nedashovat, pokud je příliš blízko
                           # Cíl mírně za hráče pro efekt "přestřelení"
                           target_dist = dist + BOSS_SIZE * 2
                           target_x = enemy['x'] + (dx / dist) * target_dist
                           target_y = enemy['y'] + (dy / dist) * target_dist

                           enemy['dash_target_x'] = target_x
                           enemy['dash_target_y'] = target_y
                           enemy['is_dashing'] = True
                           enemy['dash_timer'] = BOSS_DASH_DURATION
                        enemy['action_cooldown'] = random.uniform(BOSS_ACTION_COOLDOWN_MIN, BOSS_ACTION_COOLDOWN_MAX) # Reset plného cooldownu

                    elif action == 'summon':
                        count = random.randint(BOSS_SUMMON_COUNT_MIN, BOSS_SUMMON_COUNT_MAX)
                        spawn_minions(enemy['x'], enemy['y'], count, current_wave)
                        enemy['action_cooldown'] = random.uniform(BOSS_ACTION_COOLDOWN_MIN * 1.2, BOSS_ACTION_COOLDOWN_MAX * 1.5) # Delší cooldown po summonu

                    else: # action == 'move'
                        # Jen reset cooldownu, boss se bude normálně pohybovat
                         enemy['action_cooldown'] = random.uniform(BOSS_ACTION_COOLDOWN_MIN * 0.3, BOSS_ACTION_COOLDOWN_MAX * 0.6) # Krátká pauza

                # --- Běžný pohyb bosse (pokud nedashuje) - Optimalizováno ---
                if not enemy['is_dashing']:
                    dx = player_stats['x'] - enemy['x']
                    dy = player_stats['y'] - enemy['y']
                    # Použití čtverce vzdálenosti místo sqrt pro rychlejší výpočet
                    dist_squared = dx*dx + dy*dy
                    min_dist_squared = (BOSS_SIZE * 0.6) ** 2

                    if dist_squared > min_dist_squared: # Pohybuje se, jen když není moc blízko
                        # Použití inverzního odmocnění pro normalizaci vektoru (rychlejší)
                        dist = math.sqrt(dist_squared)  # Sqrt jen když je potřeba
                        inv_dist = 1.0 / dist if dist > 0 else 0
                        move_ex = dx * inv_dist * enemy['speed'] * dt
                        move_ey = dy * inv_dist * enemy['speed'] * dt
                        enemy['x'] += move_ex
                        enemy['y'] += move_ey

            # <<< Logika Běžných Nepřátel & Minionů - Optimalizováno >>>
            else:
                dx = player_stats['x'] - enemy['x']
                dy = player_stats['y'] - enemy['y']
                dist_squared = dx*dx + dy*dy
                dist = math.sqrt(dist_squared)  # Sqrt jen když je potřeba

                # Střelba nepřátel od určité vlny
                if current_wave >= ENEMY_SHOOTING_START_WAVE and not enemy.get('is_minion', False):
                    # Kontrola, zda nepřítel může střílet
                    if not 'can_shoot' in enemy:
                        # Při prvním vytvoření nepřítele určíme, zda bude moci střílet
                        enemy['can_shoot'] = random.random() < ENEMY_SHOOTING_CHANCE
                        enemy['shoot_cooldown'] = 0.0

                    # Pokud nepřítel může střílet a cooldown vypršel
                    if enemy['can_shoot'] and enemy['shoot_cooldown'] <= 0:
                        # Střelba na hráče
                        if dist > 0 and dist < 400:  # Střílí jen v určité vzdálenosti
                            dir_x, dir_y = dx / dist, dy / dist

                            # Vytvoření střely
                            enemy_bullet = {
                                'x': enemy['x'] + dir_x * (ENEMY_BASE_SIZE / 2),
                                'y': enemy['y'] + dir_y * (ENEMY_BASE_SIZE / 2),
                                'vx': dir_x * ENEMY_BULLET_SPEED,
                                'vy': dir_y * ENEMY_BULLET_SPEED,
                                'rect': pygame.Rect(0, 0, ENEMY_BULLET_SIZE, ENEMY_BULLET_SIZE),
                                'damage': ENEMY_BULLET_DAMAGE
                            }
                            enemy_bullet['rect'].center = (int(enemy_bullet['x']), int(enemy_bullet['y']))
                            enemy_bullets.append(enemy_bullet)

                            # Reset cooldownu
                            enemy['shoot_cooldown'] = ENEMY_SHOOTING_COOLDOWN * (0.8 + random.random() * 0.4)  # Malá náhodnost

                    # Aktualizace cooldownu
                    if enemy['shoot_cooldown'] > 0:
                        enemy['shoot_cooldown'] -= dt

                # Pohyb nepřítele
                if dist_squared > 0:
                    # Použití inverzního odmocnění pro normalizaci vektoru (rychlejší)
                    inv_dist = 1.0 / dist
                    move_ex = dx * inv_dist * enemy['speed'] * dt
                    move_ey = dy * inv_dist * enemy['speed'] * dt
                    enemy['x'] += move_ex
                    enemy['y'] += move_ey

            # Aktualizace rect pozice pro všechny typy
            enemy_size = enemy.get('size', ENEMY_BASE_SIZE)
            enemy['rect'].width = enemy_size
            enemy['rect'].height = enemy_size
            enemy['rect'].center = (int(enemy['x']), int(enemy['y']))

            # Kontrola kolizí s ostatními nepřáteli a jejich odtlačení
            for j, other_enemy in enumerate(enemies):
                if i != j and other_enemy['health'] > 0:  # Nekontrolujeme sami sebe a mrtvé nepřátele
                    # Rychlá kontrola vzdálenosti
                    dx = enemy['x'] - other_enemy['x']
                    dy = enemy['y'] - other_enemy['y']
                    dist_squared = dx*dx + dy*dy

                    # Minimální vzdálenost mezi nepřáteli (součet jejich poloměrů)
                    other_size = other_enemy.get('size', ENEMY_BASE_SIZE)
                    min_dist = (enemy_size + other_size) / 2.0
                    min_dist_squared = min_dist * min_dist

                    # Pokud jsou příliš blízko, odtlačíme je od sebe
                    if dist_squared < min_dist_squared:
                        # Výpočet směru a síly odtlačení
                        dist = math.sqrt(dist_squared)
                        if dist > 0:  # Prevence dělení nulou
                            overlap = min_dist - dist
                            # Normalizovaný vektor směru
                            nx = dx / dist
                            ny = dy / dist

                            # Aplikace odtlačení - silnější pro běžné nepřátele, slabší pro bosse
                            push_factor = 0.5  # Faktor síly odtlačení
                            if enemy.get('is_boss', False) or other_enemy.get('is_boss', False):
                                push_factor = 0.2  # Slabší odtlačení pro bosse

                            # Odtlačení aktuálního nepřítele
                            enemy['x'] += nx * overlap * push_factor
                            enemy['y'] += ny * overlap * push_factor

                            # Odtlačení druhého nepřítele v opačném směru
                            other_enemy['x'] -= nx * overlap * push_factor
                            other_enemy['y'] -= ny * overlap * push_factor

                            # Aktualizace rect pozice druhého nepřítele
                            other_enemy['rect'].center = (int(other_enemy['x']), int(other_enemy['y']))

            # Optimalizovaná kolize s hráčem pro všechny typy
            # Nejprve rychlá kontrola vzdálenosti (čtverec vzdálenosti - bez sqrt)
            dx = player_stats['x'] - enemy['x']
            dy = player_stats['y'] - enemy['y']
            dist_squared = dx*dx + dy*dy
            enemy_radius_squared = (enemy_size/2) ** 2
            collision_threshold_squared = player_collision_radius_squared + enemy_radius_squared

            # Přesná kontrola kolize jen pokud je potřeba
            potential_collision = dist_squared < collision_threshold_squared * 1.2  # Trochu větší pro jistotu

            if potential_collision and enemy['rect'].colliderect(player_rect_for_collision):
                player_stats['health'] -= enemy['damage']
                sound_manager.play_sound('player_hurt')
                if i not in enemies_to_remove_indices:
                    enemies_to_remove_indices.append(i)
                    enemies_alive_in_wave -= 1
                if player_stats['health'] <= 0:
                    player_stats['health'] = 0

        # Odstranění nepřátel
        unique_indices_to_remove = sorted(list(set(enemies_to_remove_indices)), reverse=True)
        for i in unique_indices_to_remove:
             if i < len(enemies): enemies.pop(i)

        # Kontrola smrti HP
        if player_stats['health'] <= 0 and game_state == STATE_PLAYING:
             print("Player ran out of health!")
             game_state = STATE_GAME_OVER

    elif game_state == STATE_SHOP: # Upravená logika pro zbraně
        shop_item_rects.clear()
        for i, item in enumerate(shop_items):
            # Výpočet pozice položky v mřížce
            col = i % SHOP_COLS
            row = i // SHOP_COLS
            item_x = SHOP_GRID_START_X + col * (SHOP_ITEM_WIDTH + SHOP_PADDING_X)
            item_y = SHOP_GRID_START_Y + row * (SHOP_ITEM_HEIGHT + SHOP_PADDING_Y)
            item_rect = pygame.Rect(item_x, item_y, SHOP_ITEM_WIDTH, SHOP_ITEM_HEIGHT)
            shop_item_rects.append(item_rect)

            # Kontrola, zda je položka zbraň a zda ji již vlastníme
            is_weapon = item['id'] in ['shotgun', 'machinegun']
            is_owned = False

            if is_weapon:
                if item['id'] == 'shotgun':
                    is_owned = weapons[WEAPON_SHOTGUN]['owned']
                elif item['id'] == 'machinegun':
                    is_owned = weapons[WEAPON_MACHINEGUN]['owned']

            # Kontrola, zda si můžeme položku dovolit
            can_afford = score >= item['cost'] and not is_owned
            is_hovering = item_rect.collidepoint(mouse_pos) and can_afford

            # Nákup položky
            if mouse_click and is_hovering:
                print(f"Purchase: {item['name']}")
                item['effect'](player_stats)
                score -= item['cost']
                print(f"Score left: {score}")

                # Pokud jsme koupili zbraň, automaticky ji vybereme
                if item['id'] == 'shotgun' and weapons[WEAPON_SHOTGUN]['owned']:
                    player_stats['current_weapon'] = WEAPON_SHOTGUN
                    print("Switched to Shotgun")
                elif item['id'] == 'machinegun' and weapons[WEAPON_MACHINEGUN]['owned']:
                    player_stats['current_weapon'] = WEAPON_MACHINEGUN
                    print("Switched to Machine Gun")

        # Tlačítko pro odchod z obchodu
        exit_button_rect = pygame.Rect(SCREEN_WIDTH//2 - 100, SCREEN_HEIGHT - 80, 200, 50)
        if mouse_click and exit_button_rect.collidepoint(mouse_pos):
            game_state = STATE_PLAYING
    elif game_state == STATE_GAME_OVER: # logika beze změny
        if score > high_score: high_score = score; save_highscore(high_score)

    # =============================
    # ---     VYKRESLOVÁNÍ      ---
    # =============================
    glClearColor(0.0, 0.0, 0.0, 1.0); glClear(GL_COLOR_BUFFER_BIT)
    if game_state == STATE_MAIN_MENU:
        if menu_shader_program:
            try: glUseProgram(menu_shader_program); glUniform2f(loc_menu_screenResolution, float(SCREEN_WIDTH), float(SCREEN_HEIGHT)); glUniform1f(loc_menu_time, float(current_timestamp)); glBindVertexArray(VAO); glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, None); glBindVertexArray(0); glUseProgram(0)
            except Exception as e: print(f"Menu shader draw error: {e}"); glUseProgram(0)
    if game_state == STATE_PLAYING: draw_surface.fill(DARK_GREY)
    else: draw_surface.fill(TRANSPARENT)

    # Kreslení prvků na draw_surface
    if game_state == STATE_SPLASH:
        # Vykreslení splash screenu
        draw_splash_screen(draw_surface, splash_alpha)

    elif game_state == STATE_MAIN_MENU:
        # Vykreslení hlavního menu
        start_hover, settings_hover, quit_hover = draw_main_menu(draw_surface, high_score)

    elif game_state == STATE_SETTINGS:
        # Vykreslení nastavení
        back_hover, tab_rects = draw_settings_menu(draw_surface, settings, current_settings_tab, settings_tabs)

    elif game_state == STATE_PLAYING:
        # Vykreslení hráče pomocí assetu
        player_center = (int(player_stats['x']), int(player_stats['y']))

        # Definujeme player_radius pro použití při vykreslování zbraní
        player_radius = PLAYER_SIZE // 2

        # Použití nového assetu, pokud je k dispozici
        if 'player' in game_assets:
            player_img = game_assets['player']
            # Získání rozměrů obrázku
            player_width = player_img.get_width()
            player_height = player_img.get_height()
            # Vykreslení hráče na střed jeho pozice
            player_rect = player_img.get_rect(center=player_center)
            draw_surface.blit(player_img, player_rect.topleft)
        else:
            # Záložní vykreslení, pokud asset není k dispozici
            # Stín hráče pro 3D efekt
            shadow_offset = 3
            pygame.draw.circle(draw_surface, DARK_GREY, (player_center[0] + shadow_offset, player_center[1] + shadow_offset), player_radius)
            # Hlavní tělo - kruh
            pygame.draw.circle(draw_surface, WHITE, player_center, player_radius)
            # Obličej hráče - vnitřní kruh
            face_color = (255, 220, 180)  # Barva obličeje
            face_radius = int(player_radius * 0.85)
            pygame.draw.circle(draw_surface, face_color, player_center, face_radius)
            # Oči
            eye_size = int(player_radius * 0.3)
            eye_offset = int(player_radius * 0.4)  # Vzdálenost očí od středu
            eye_y = player_center[1] - int(player_radius * 0.15)  # Mírně nad středem
            left_eye_x = player_center[0] - eye_offset
            right_eye_x = player_center[0] + eye_offset
            # Směr očí sleduje myš
            mouse_pos = pygame.mouse.get_pos()
            dx = mouse_pos[0] - player_stats['x']
            dy = mouse_pos[1] - player_stats['y']
            angle = math.atan2(dy, dx)
            eye_offset_x = int(eye_size * 0.3 * math.cos(angle))
            eye_offset_y = int(eye_size * 0.3 * math.sin(angle))
            # Bílé části očí
            pygame.draw.circle(draw_surface, WHITE, (left_eye_x, eye_y), eye_size)
            pygame.draw.circle(draw_surface, WHITE, (right_eye_x, eye_y), eye_size)
            # Zorničky (sledují myš)
            pupil_size = int(eye_size * 0.5)
            pygame.draw.circle(draw_surface, BLACK, (left_eye_x + eye_offset_x, eye_y + eye_offset_y), pupil_size)
            pygame.draw.circle(draw_surface, BLACK, (right_eye_x + eye_offset_x, eye_y + eye_offset_y), pupil_size)
            # Ústa - úsměv nebo zamračení podle zdraví
            mouth_width = int(player_radius * 0.8)
            mouth_height = int(player_radius * 0.3)
            mouth_y = player_center[1] + int(player_radius * 0.3)  # Pod středem
            mouth_x = player_center[0] - mouth_width // 2
            health_ratio = player_stats['health'] / player_stats['max_health']
            if health_ratio > 0.5:  # Úsměv při dobrém zdraví
                # Úsměv - oblouk dolů
                pygame.draw.arc(draw_surface, BLACK,
                               (mouth_x, mouth_y - mouth_height//2,
                                mouth_width, mouth_height),
                               0, math.pi, 2)
            else:  # Zamračení při nízkém zdraví
                # Zamračení - oblouk nahoru
                pygame.draw.arc(draw_surface, BLACK,
                               (mouth_x, mouth_y,
                                mouth_width, mouth_height),
                               math.pi, 2*math.pi, 2)

        # Vykreslení aktuální zbraně - vizuální reprezentace podle typu
        current_weapon = weapons[player_stats['current_weapon']]

        # Výpočet úhlu mezi hráčem a myší pro rotaci zbraně
        mouse_pos = pygame.mouse.get_pos()
        dx = mouse_pos[0] - player_center[0]
        dy = mouse_pos[1] - player_center[1]
        weapon_angle = math.atan2(dy, dx)

        # Vzdálenost zbraně od středu hráče - zvětšeno pro lepší viditelnost
        weapon_distance = player_radius + 15

        # Pozice zbraně - rotuje kolem hráče podle pozice myši
        weapon_base_x = player_center[0] + math.cos(weapon_angle) * weapon_distance
        weapon_base_y = player_center[1] + math.sin(weapon_angle) * weapon_distance

        # Uložení pozice konce hlavně pro pozdější použití při střelbě
        weapon_muzzle_x = 0
        weapon_muzzle_y = 0

        # Získání typu zbraně pro asset
        weapon_type_names = ["pistol", "shotgun", "machinegun"]
        weapon_type = weapon_type_names[player_stats['current_weapon']]

        # Použití nového assetu, pokud je k dispozici
        if 'weapons' in game_assets and weapon_type in game_assets['weapons']:
            weapon_img = game_assets['weapons'][weapon_type]

            # Rotace zbraně
            rotated_weapon = pygame.transform.rotate(weapon_img, -math.degrees(weapon_angle))

            # Pozice rotované zbraně
            weapon_rect = rotated_weapon.get_rect(center=(weapon_base_x, weapon_base_y))

            # Vykreslení
            draw_surface.blit(rotated_weapon, weapon_rect.topleft)

            # Výpočet pozice konce hlavně pro střely
            # Délka zbraně závisí na typu
            if player_stats['current_weapon'] == WEAPON_PISTOL:
                barrel_length = 30
            elif player_stats['current_weapon'] == WEAPON_SHOTGUN:
                barrel_length = 45
            else:  # WEAPON_MACHINEGUN
                barrel_length = 40

            weapon_muzzle_x = player_center[0] + math.cos(weapon_angle) * (weapon_distance + barrel_length)
            weapon_muzzle_y = player_center[1] + math.sin(weapon_angle) * (weapon_distance + barrel_length)
        else:
            # Záložní vykreslení, pokud asset není k dispozici
            # Základní parametry zbraně
            weapon_base_width = 20
            weapon_base_height = 10

            if player_stats['current_weapon'] == WEAPON_PISTOL:
                # Pistole - malý čtverec
                # Vytvoření povrchu pro zbraň
                weapon_surface = pygame.Surface((weapon_base_width + 5, weapon_base_height), pygame.SRCALPHA)
                # Tělo zbraně
                pygame.draw.rect(weapon_surface, current_weapon['color'],
                                (0, 0, weapon_base_width, weapon_base_height), border_radius=2)
                # Hlaveň
                pygame.draw.rect(weapon_surface, DARK_GREY,
                                (weapon_base_width, weapon_base_height//2 - 3, 5, 6), border_radius=1)

                # Rotace zbraně
                rotated_weapon = pygame.transform.rotate(weapon_surface, -math.degrees(weapon_angle))
                # Pozice rotované zbraně
                weapon_rect = rotated_weapon.get_rect(center=(weapon_base_x, weapon_base_y))
                # Vykreslení
                draw_surface.blit(rotated_weapon, weapon_rect.topleft)

                # Výpočet pozice konce hlavně pro střely
                barrel_length = weapon_base_width + 5
                weapon_muzzle_x = player_center[0] + math.cos(weapon_angle) * (weapon_distance + barrel_length)
                weapon_muzzle_y = player_center[1] + math.sin(weapon_angle) * (weapon_distance + barrel_length)

            elif player_stats['current_weapon'] == WEAPON_SHOTGUN:
                # Brokovnice - delší obdélník
                shotgun_width = int(weapon_base_width * 1.5)
                shotgun_height = weapon_base_height
                barrel_length = 10

                # Vytvoření povrchu pro zbraň
                weapon_surface = pygame.Surface((shotgun_width + barrel_length, shotgun_height + 2), pygame.SRCALPHA)
                # Tělo zbraně
                pygame.draw.rect(weapon_surface, current_weapon['color'],
                                (0, 1, shotgun_width, shotgun_height), border_radius=2)
                # Delší hlaveň
                pygame.draw.rect(weapon_surface, DARK_GREY,
                                (shotgun_width, 0, barrel_length, shotgun_height + 2), border_radius=1)

                # Rotace zbraně
                rotated_weapon = pygame.transform.rotate(weapon_surface, -math.degrees(weapon_angle))
                # Pozice rotované zbraně
                weapon_rect = rotated_weapon.get_rect(center=(weapon_base_x, weapon_base_y))
                # Vykreslení
                draw_surface.blit(rotated_weapon, weapon_rect.topleft)

                # Výpočet pozice konce hlavně pro střely
                total_length = shotgun_width + barrel_length
                weapon_muzzle_x = player_center[0] + math.cos(weapon_angle) * (weapon_distance + total_length)
                weapon_muzzle_y = player_center[1] + math.sin(weapon_angle) * (weapon_distance + total_length)

            elif player_stats['current_weapon'] == WEAPON_MACHINEGUN:
                # Kulomet - široký obdélník s detaily
                machinegun_width = int(weapon_base_width * 1.2)
                machinegun_height = weapon_base_height + 4
                barrel_length = 8

                # Vytvoření povrchu pro zbraň
                weapon_surface = pygame.Surface((machinegun_width + barrel_length, machinegun_height + 6), pygame.SRCALPHA)
                # Tělo zbraně
                pygame.draw.rect(weapon_surface, current_weapon['color'],
                                (0, 0, machinegun_width, machinegun_height), border_radius=3)
                # Hlaveň kulometu
                pygame.draw.rect(weapon_surface, DARK_GREY,
                                (machinegun_width, machinegun_height//2 - 3, barrel_length, 6), border_radius=1)
                # Zásobník
                pygame.draw.rect(weapon_surface, DARK_GREY,
                                (machinegun_width//2, machinegun_height, 8, 6), border_radius=1)

                # Rotace zbraně
                rotated_weapon = pygame.transform.rotate(weapon_surface, -math.degrees(weapon_angle))
                # Pozice rotované zbraně
                weapon_rect = rotated_weapon.get_rect(center=(weapon_base_x, weapon_base_y))
                # Vykreslení
                draw_surface.blit(rotated_weapon, weapon_rect.topleft)

                # Výpočet pozice konce hlavně pro střely
                total_length = machinegun_width + barrel_length
                weapon_muzzle_x = player_center[0] + math.cos(weapon_angle) * (weapon_distance + total_length)
                weapon_muzzle_y = player_center[1] + math.sin(weapon_angle) * (weapon_distance + total_length)

        # Uložení pozice konce hlavně do globální proměnné pro použití při střelbě
        player_stats['weapon_muzzle_x'] = weapon_muzzle_x
        player_stats['weapon_muzzle_y'] = weapon_muzzle_y

        # Kreslení pastí
        for trap in traps:
            if not trap['triggered']:
                trap_rect = trap['rect']

                # Různé vykreslení podle typu pasti
                if trap['type'] == TRAP_TYPE_SPIKE:
                    # Bodce - ostré trojúhelníky
                    pygame.draw.rect(draw_surface, trap['color'], trap_rect, border_radius=2)
                    # Vykreslení bodců
                    spike_size = TRAP_SPIKE_SIZE // 4
                    for i in range(4):
                        for j in range(4):
                            spike_x = trap_rect.x + i * spike_size + spike_size // 2
                            spike_y = trap_rect.y + j * spike_size + spike_size // 2
                            pygame.draw.polygon(draw_surface, DARK_GREY, [
                                (spike_x, spike_y - spike_size // 2),
                                (spike_x - spike_size // 2, spike_y + spike_size // 2),
                                (spike_x + spike_size // 2, spike_y + spike_size // 2)
                            ])

                elif trap['type'] == TRAP_TYPE_POISON:
                    # Jedová past - zelená kaluž
                    pygame.draw.ellipse(draw_surface, trap['color'], trap_rect)
                    # Bublinky
                    for _ in range(3):
                        bubble_x = random.randint(trap_rect.left, trap_rect.right)
                        bubble_y = random.randint(trap_rect.top, trap_rect.bottom)
                        bubble_size = random.randint(3, 6)
                        pygame.draw.circle(draw_surface, (200, 255, 200), (bubble_x, bubble_y), bubble_size)

                elif trap['type'] == TRAP_TYPE_SLOW:
                    # Zpomalovací past - modrá ledová plocha
                    pygame.draw.ellipse(draw_surface, trap['color'], trap_rect)
                    # Ledové krystaly
                    for _ in range(5):
                        crystal_x = random.randint(trap_rect.left, trap_rect.right)
                        crystal_y = random.randint(trap_rect.top, trap_rect.bottom)
                        crystal_size = random.randint(2, 5)
                        pygame.draw.rect(draw_surface, WHITE, (crystal_x, crystal_y, crystal_size, crystal_size))

                elif trap['type'] == TRAP_TYPE_EXPLOSION:
                    # Výbuch - červená koule
                    pygame.draw.ellipse(draw_surface, TRAP_EXPLOSION_COLOR, trap_rect)
                    # Exploze
                    for _ in range(5):
                        explosion_x = random.randint(trap_rect.left, trap_rect.right)
                        explosion_y = random.randint(trap_rect.top, trap_rect.bottom)
                        explosion_size = random.randint(5, 15)
                        pygame.draw.circle(draw_surface, (255, 0, 0), (explosion_x, explosion_y), explosion_size)

                elif trap['type'] == TRAP_TYPE_TELEPORT:
                    # Teleportace - modrá koule
                    pygame.draw.ellipse(draw_surface, TRAP_TELEPORT_COLOR, trap_rect)
                    # Teleportační efekt
                    for _ in range(5):
                        teleport_x = random.randint(trap_rect.left, trap_rect.right)
                        teleport_y = random.randint(trap_rect.top, trap_rect.bottom)
                        teleport_size = random.randint(5, 15)
                        pygame.draw.circle(draw_surface, (0, 0, 255), (teleport_x, teleport_y), teleport_size)

        # Kreslení nepřátel (včetně bosse a minionů) s novými assety
        for enemy in enemies:
            # Určení typu nepřítele pro výběr assetu
            enemy_type = "basic"  # Výchozí typ
            if enemy.get('is_boss'):
                enemy_type = "boss"
            elif enemy.get('is_minion'):
                enemy_type = "basic"  # Minioni používají stejný asset jako základní nepřátelé
            elif enemy.get('speed', 0) > ENEMY_BASE_SPEED * 1.5:
                enemy_type = "fast"  # Rychlí nepřátelé mají vyšší rychlost

            # Použití nového assetu, pokud je k dispozici
            if 'enemies' in game_assets and enemy_type in game_assets['enemies']:
                enemy_img = game_assets['enemies'][enemy_type]
                # Vykreslení nepřítele na střed jeho pozice
                enemy_rect = enemy_img.get_rect(center=enemy['rect'].center)
                draw_surface.blit(enemy_img, enemy_rect.topleft)
            else:
                # Záložní vykreslení, pokud asset není k dispozici
                color = BOSS_COLOR if enemy.get('is_boss') else MINION_COLOR if enemy.get('is_minion') else ENEMY_COLOR
                pygame.draw.rect(draw_surface, color, enemy['rect'])

            # HP Bar - vždy se vykresluje
            if enemy['max_health'] > 0:
                hp_ratio = max(0, enemy['health'] / enemy['max_health'])
                hp_bar_width = int(enemy['rect'].width * hp_ratio)
                hp_bar_rect = pygame.Rect(enemy['rect'].left, enemy['rect'].top - 7, hp_bar_width, 5)
                pygame.draw.rect(draw_surface, GREEN, hp_bar_rect)

        # Kreslení střel hráče s novými assety
        for bullet in bullets:
            # Určení typu střely podle barvy
            bullet_type = "basic"  # Výchozí typ
            bullet_color = bullet.get('color', BULLET_COLOR)

            if bullet_color == weapons[WEAPON_SHOTGUN]['color']:
                bullet_type = "shotgun"
            elif bullet_color == weapons[WEAPON_MACHINEGUN]['color']:
                bullet_type = "basic"  # Kulomet používá základní střely

            # Použití nového assetu, pokud je k dispozici
            if 'bullets' in game_assets and bullet_type in game_assets['bullets']:
                bullet_img = game_assets['bullets'][bullet_type]
                # Vykreslení střely na střed její pozice
                bullet_rect = bullet_img.get_rect(center=bullet['rect'].center)
                draw_surface.blit(bullet_img, bullet_rect.topleft)
            else:
                # Záložní vykreslení, pokud asset není k dispozici
                bullet_size = bullet['rect'].width // 2 + 1
                pygame.draw.circle(draw_surface, bullet_color, bullet['rect'].center, bullet_size)

        # Kreslení střel bosse s novým assetem
        for b_bullet in boss_bullets:
            # Použití nového assetu, pokud je k dispozici
            if 'bullets' in game_assets and 'boss' in game_assets['bullets']:
                boss_bullet_img = game_assets['bullets']['boss']
                # Vykreslení střely na střed její pozice
                boss_bullet_rect = boss_bullet_img.get_rect(center=b_bullet['rect'].center)
                draw_surface.blit(boss_bullet_img, boss_bullet_rect.topleft)
            else:
                # Záložní vykreslení, pokud asset není k dispozici
                pygame.draw.rect(draw_surface, BOSS_PROJECTILE_COLOR, b_bullet['rect'])

        # Kreslení střel nepřátel
        for e_bullet in enemy_bullets:
            # Použití nového assetu, pokud je k dispozici
            if 'bullets' in game_assets and 'shotgun' in game_assets['bullets']:
                # Použijeme asset střely brokovnice pro nepřátele
                enemy_bullet_img = game_assets['bullets']['shotgun']
                # Vykreslení střely na střed její pozice
                enemy_bullet_rect = enemy_bullet_img.get_rect(center=e_bullet['rect'].center)
                draw_surface.blit(enemy_bullet_img, enemy_bullet_rect.topleft)
            else:
                # Záložní vykreslení, pokud asset není k dispozici
                pygame.draw.circle(draw_surface, ENEMY_BULLET_COLOR, e_bullet['rect'].center, e_bullet['rect'].width // 2)

        # Herní UI s plným pozadím pro lepší čitelnost
        # Pozadí pro HP bar
        hp_bg_width = 250
        hp_bg_height = 60
        hp_bg_x = 10
        hp_bg_y = 10
        hp_bg = pygame.Surface((hp_bg_width, hp_bg_height))
        hp_bg.fill((0, 0, 0))  # Černé bez průhlednosti
        draw_surface.blit(hp_bg, (hp_bg_x, hp_bg_y))

        # HP bar
        health_ratio = max(0, player_stats['health']/player_stats['max_health'])
        health_bar_width = 200
        health_bar_height = 20
        health_bar_x = hp_bg_x + 10
        health_bar_y = hp_bg_y + 20
        current_health_width = int(health_bar_width * health_ratio)

        # Vykreslení HP textu nad barem
        hp_title = "HEALTH"
        draw_text_topleft(draw_surface, hp_title, small_font, WHITE, (health_bar_x, health_bar_y - 15))

        # Vykreslení HP baru s 3D efektem
        # Stín pro 3D efekt
        pygame.draw.rect(draw_surface, (50, 0, 0), (health_bar_x + 2, health_bar_y + 2, health_bar_width, health_bar_height), border_radius=3)
        # Pozadí baru
        pygame.draw.rect(draw_surface, RED, (health_bar_x, health_bar_y, health_bar_width, health_bar_height), border_radius=3)
        # Aktuální zdraví
        if current_health_width > 0:
            # Gradient efekt pro zdraví
            for i in range(current_health_width):
                # Gradient od tmavší k světlejší zelené
                gradient_factor = i / health_bar_width
                r = int(0 * (0.7 + 0.3 * gradient_factor))
                g = int(255 * (0.7 + 0.3 * gradient_factor))
                b = int(0 * (0.7 + 0.3 * gradient_factor))
                pygame.draw.line(draw_surface, (r, g, b),
                               (health_bar_x + i, health_bar_y),
                               (health_bar_x + i, health_bar_y + health_bar_height), 1)

        # Okraj baru
        pygame.draw.rect(draw_surface, WHITE, (health_bar_x, health_bar_y, health_bar_width, health_bar_height), 2, border_radius=3)

        # HP text s hodnotami
        hp_text = f"{int(player_stats['health'])}/{int(player_stats['max_health'])}"
        draw_text_topleft(draw_surface, hp_text, small_font, WHITE, (health_bar_x + health_bar_width + 5, health_bar_y + 2))

        # Pozadí pro skóre a další informace - poloprůhledné
        info_bg_width = 400
        info_bg_height = 100
        info_bg_x = SCREEN_WIDTH // 2 - info_bg_width // 2
        info_bg_y = 10
        info_bg = pygame.Surface((info_bg_width, info_bg_height), pygame.SRCALPHA)
        info_bg.fill((0, 0, 0, 120))  # Poloprůhledné černé pozadí
        draw_surface.blit(info_bg, (info_bg_x, info_bg_y))

        # Skóre a další informace
        draw_text(draw_surface, f"Score: {score}", default_font, YELLOW, (SCREEN_WIDTH // 2, 25), with_bg=False)

        # Pozadí pro světlo - poloprůhledné
        light_bg_width = 150
        light_bg_height = 60
        light_bg_x = SCREEN_WIDTH - light_bg_width - 10
        light_bg_y = 10
        light_bg = pygame.Surface((light_bg_width, light_bg_height), pygame.SRCALPHA)
        light_bg.fill((0, 0, 0, 120))  # Poloprůhledné černé pozadí
        draw_surface.blit(light_bg, (light_bg_x, light_bg_y))

        # Světlo
        light_text = f"Light: {int(player_stats['light_radius'])}"
        draw_text(draw_surface, light_text, default_font, WHITE, (SCREEN_WIDTH - 75, 25), with_bg=False)

        # Informace o vlně
        wave_ui_text = f"Wave: {current_wave} | Enemies: {enemies_alive_in_wave}"
        draw_text(draw_surface, wave_ui_text, default_font, ORANGE, (SCREEN_WIDTH // 2, 60), with_bg=False)

        # Zobrazení informací o aktuální zbrani
        current_weapon = weapons[player_stats['current_weapon']]
        weapon_text = f"Weapon: {current_weapon['name']}"
        draw_text(draw_surface, weapon_text, small_font, current_weapon['color'], (SCREEN_WIDTH // 2, 90), with_bg=False)

        # Zobrazení klávesových zkratek pro zbraně
        weapon_keys_text = ""
        if weapons[WEAPON_PISTOL]['owned']:
            weapon_keys_text += "1:Pistol "
        if weapons[WEAPON_SHOTGUN]['owned']:
            weapon_keys_text += "2:Shotgun "
        if weapons[WEAPON_MACHINEGUN]['owned']:
            weapon_keys_text += "3:MachineGun"

        if weapon_keys_text:
            draw_text(draw_surface, weapon_keys_text, smaller_font, GREY, (SCREEN_WIDTH // 2, 115), with_bg=False)

        # Moderní tabulka výzev - zobrazuje se pouze při stisknutí klávesy Tab
        keys_pressed = pygame.key.get_pressed()
        if keys_pressed[pygame.K_TAB]:
            # Poloprůhledné pozadí jen pro oblast tabulky, ne celou obrazovku
            table_width = 650
            table_height = 400
            table_x = SCREEN_WIDTH // 2 - table_width // 2
            table_y = 80

            # Pozadí tabulky - poloprůhledné
            challenges_bg = pygame.Surface((table_width, table_height), pygame.SRCALPHA)
            challenges_bg.fill((0, 0, 0, 180))  # Poloprůhledné černé pozadí
            draw_surface.blit(challenges_bg, (table_x, table_y))

            # Okraj tabulky
            pygame.draw.rect(draw_surface, WHITE, (table_x, table_y, table_width, table_height), 2, border_radius=10)

            # Nadpis tabulky
            title_y = table_y + 30
            draw_text(draw_surface, "CHALLENGES", large_font, YELLOW, (SCREEN_WIDTH // 2, title_y))

            # Záhlaví tabulky
            header_y = title_y + 40

            # Moderní záhlaví s barevným pozadím
            header_bg = pygame.Rect(table_x + 20, header_y - 15, table_width - 40, 30)
            pygame.draw.rect(draw_surface, (60, 60, 80), header_bg, border_radius=5)

            # Texty záhlaví
            draw_text(draw_surface, "Challenge", default_font, WHITE, (table_x + 150, header_y))
            draw_text(draw_surface, "Progress", default_font, WHITE, (table_x + 350, header_y))
            draw_text(draw_surface, "Reward", default_font, WHITE, (table_x + 500, header_y))

            # Oddělovací čára
            pygame.draw.line(draw_surface, (100, 100, 120), (table_x + 20, header_y + 20), (table_x + table_width - 20, header_y + 20), 1)

            # Výzvy
            for i, challenge in enumerate(challenges):
                row_y = header_y + 50 + i * 60

                # Pozadí řádku - střídavé barvy pro lepší čitelnost
                if i % 2 == 0:
                    row_bg_color = (40, 40, 50, 150)  # Tmavší
                else:
                    row_bg_color = (50, 50, 60, 150)  # Světlejší

                row_bg = pygame.Rect(table_x + 20, row_y - 20, table_width - 40, 50)
                pygame.draw.rect(draw_surface, row_bg_color, row_bg, border_radius=5)

                # Ikona výzvy (různé ikony podle typu výzvy)
                icon_x = table_x + 50
                icon_y = row_y
                icon_size = 30
                icon_bg = pygame.Rect(icon_x - icon_size//2 - 5, icon_y - icon_size//2 - 5, icon_size + 10, icon_size + 10)

                # Pozadí ikony
                pygame.draw.circle(draw_surface, (70, 70, 90), (icon_x, icon_y), icon_size//2 + 5)

                if 'kills_required' in challenge:
                    if 'weapon_type' in challenge and challenge['weapon_type'] == WEAPON_SHOTGUN:
                        # Ikona pro shotgun výzvu - speciální ikona
                        pygame.draw.rect(draw_surface, RED,
                                        (icon_x - icon_size//3, icon_y - icon_size//6,
                                         icon_size*2//3, icon_size//3), border_radius=2)
                        # Hlaveň
                        pygame.draw.rect(draw_surface, WHITE,
                                        (icon_x + icon_size//3, icon_y - icon_size//8,
                                         icon_size//3, icon_size//4), border_radius=1)
                    else:
                        # Ikona pro zabíjení nepřátel - meč
                        pygame.draw.polygon(draw_surface, RED, [
                            (icon_x, icon_y - icon_size//2),
                            (icon_x + icon_size//3, icon_y),
                            (icon_x, icon_y + icon_size//2),
                            (icon_x - icon_size//3, icon_y)
                        ])
                elif 'traps_required' in challenge:
                    # Ikona pro pasti - trojúhelník
                    pygame.draw.polygon(draw_surface, TRAP_POISON_COLOR, [
                        (icon_x, icon_y - icon_size//2),
                        (icon_x + icon_size//2, icon_y + icon_size//2),
                        (icon_x - icon_size//2, icon_y + icon_size//2)
                    ])
                elif 'boss_kills_required' in challenge:
                    # Ikona pro zabití bossů - koruna
                    points = []
                    for j in range(5):
                        angle = math.pi/2 + j * 2*math.pi/5
                        points.append((icon_x + int(math.cos(angle) * icon_size//2),
                                      icon_y - int(math.sin(angle) * icon_size//2)))
                    pygame.draw.polygon(draw_surface, YELLOW, points)
                    # Vnitřek koruny
                    pygame.draw.circle(draw_surface, (70, 70, 90), (icon_x, icon_y), icon_size//4)
                elif 'check' in challenge:
                    if 'description' in challenge and 'light' in challenge['description'].lower():
                        # Ikona pro světlo - sluníčko
                        pygame.draw.circle(draw_surface, YELLOW, (icon_x, icon_y), icon_size//3)
                        # Paprsky
                        for j in range(8):
                            angle = j * math.pi/4
                            start_x = icon_x + int(math.cos(angle) * icon_size//3)
                            start_y = icon_y + int(math.sin(angle) * icon_size//3)
                            end_x = icon_x + int(math.cos(angle) * icon_size//2)
                            end_y = icon_y + int(math.sin(angle) * icon_size//2)
                            pygame.draw.line(draw_surface, YELLOW, (start_x, start_y), (end_x, end_y), 2)
                    else:
                        # Ikona pro vlny - vlnovka
                        for j in range(5):
                            wave_height = 3
                            if j % 2 == 0:
                                pygame.draw.line(draw_surface, ORANGE,
                                               (icon_x - icon_size//2 + j*icon_size//5, icon_y - wave_height),
                                               (icon_x - icon_size//2 + (j+1)*icon_size//5, icon_y + wave_height), 2)
                            else:
                                pygame.draw.line(draw_surface, ORANGE,
                                               (icon_x - icon_size//2 + j*icon_size//5, icon_y + wave_height),
                                               (icon_x - icon_size//2 + (j+1)*icon_size//5, icon_y - wave_height), 2)

                # Název výzvy
                draw_text(draw_surface, challenge['name'], small_font, WHITE, (table_x + 150, row_y))

                # Stav výzvy a progress bar
                progress_bar_width = 150
                progress_bar_height = 15
                progress_bar_x = table_x + 350 - progress_bar_width // 2
                progress_bar_y = row_y + 10

                # Pozadí progress baru - tmavší
                pygame.draw.rect(draw_surface, (30, 30, 40), (progress_bar_x, progress_bar_y, progress_bar_width, progress_bar_height), border_radius=7)

                # Výpočet progresu
                progress = 0
                if challenge['completed']:
                    progress = 1.0
                    status_color = GREEN
                else:
                    if 'kills_required' in challenge:
                        progress = challenge['kills_current'] / challenge['kills_required']
                        status_text = f"{challenge['kills_current']}/{challenge['kills_required']}"
                    elif 'traps_required' in challenge:
                        progress = challenge['traps_current'] / challenge['traps_required']
                        status_text = f"{challenge['traps_current']}/{challenge['traps_required']}"
                    elif 'boss_kills_required' in challenge:
                        progress = challenge['boss_kills_current'] / challenge['boss_kills_required']
                        status_text = f"{challenge['boss_kills_current']}/{challenge['boss_kills_required']}"
                    elif 'check' in challenge:
                        if 'description' in challenge and 'light' in challenge['description'].lower():
                            progress = min(player_stats['light_radius'] / 500, 1.0)
                            status_text = f"{int(player_stats['light_radius'])}/500"
                        else:
                            progress = min(current_wave / 5, 1.0)
                            status_text = f"Wave {current_wave}/5"
                    status_color = ORANGE

                # Vyplnění progress baru - gradient efekt
                progress_width = int(progress_bar_width * progress)
                if progress_width > 0:
                    for i in range(progress_width):
                        # Gradient od tmavší k světlejší barvě
                        gradient_factor = i / progress_bar_width
                        r = int(status_color[0] * (0.7 + 0.3 * gradient_factor))
                        g = int(status_color[1] * (0.7 + 0.3 * gradient_factor))
                        b = int(status_color[2] * (0.7 + 0.3 * gradient_factor))
                        pygame.draw.line(draw_surface, (r, g, b),
                                       (progress_bar_x + i, progress_bar_y),
                                       (progress_bar_x + i, progress_bar_y + progress_bar_height), 1)

                # Okraj progress baru
                pygame.draw.rect(draw_surface, (100, 100, 120), (progress_bar_x, progress_bar_y, progress_bar_width, progress_bar_height), 1, border_radius=7)

                # Text nad progress barem
                if challenge['completed']:
                    status_text = "COMPLETED"
                    # Přidání ikony dokončení
                    checkmark_x = progress_bar_x + progress_bar_width + 15
                    checkmark_y = progress_bar_y + progress_bar_height // 2
                    pygame.draw.circle(draw_surface, GREEN, (checkmark_x, checkmark_y), 8)
                    pygame.draw.line(draw_surface, WHITE,
                                   (checkmark_x - 4, checkmark_y),
                                   (checkmark_x - 1, checkmark_y + 3), 2)
                    pygame.draw.line(draw_surface, WHITE,
                                   (checkmark_x - 1, checkmark_y + 3),
                                   (checkmark_x + 4, checkmark_y - 3), 2)

                draw_text(draw_surface, status_text, smaller_font, WHITE, (table_x + 350, progress_bar_y - 10))

                # Odměna
                reward_text = f"+{challenge['reward']}"
                draw_text(draw_surface, reward_text, small_font, YELLOW, (table_x + 500, row_y))

            # Informace o klávese Tab - přesunuta do tabulky
            tab_info_y = table_y + table_height - 20
            draw_text(draw_surface, "Hold TAB to view Challenges", smaller_font, GREY, (SCREEN_WIDTH // 2, tab_info_y))

        # Zobrazení stavů efektů s poloprůhledným pozadím
        effects_y = SCREEN_HEIGHT - 60

        # Počet aktivních efektů
        active_effects = 0
        if player_stats['poisoned']:
            active_effects += 1
        if player_stats['slowed']:
            active_effects += 1

        # Pokud jsou aktivní efekty, vytvoříme pozadí
        if active_effects > 0:
            effects_bg_width = 200
            effects_bg_height = 30 * active_effects + 10
            effects_bg_x = SCREEN_WIDTH // 2 - effects_bg_width // 2
            effects_bg_y = SCREEN_HEIGHT - effects_bg_height - 20

            # Pozadí pro efekty - poloprůhledné
            effects_bg = pygame.Surface((effects_bg_width, effects_bg_height), pygame.SRCALPHA)
            effects_bg.fill((0, 0, 0, 150))  # Poloprůhledné černé pozadí
            draw_surface.blit(effects_bg, (effects_bg_x, effects_bg_y))

            # Okraj pozadí
            pygame.draw.rect(draw_surface, (100, 100, 100),
                           (effects_bg_x, effects_bg_y, effects_bg_width, effects_bg_height),
                           1, border_radius=5)

            # Pozice pro text
            effects_text_y = effects_bg_y + 20

            # Vykreslení efektů
            if player_stats['poisoned']:
                poison_text = f"POISONED: {int(player_stats['poison_timer'])}s"
                # Ikona otravy
                poison_icon_x = effects_bg_x + 30
                poison_icon_y = effects_text_y
                poison_icon_size = 12

                # Zelená kapka
                pygame.draw.circle(draw_surface, TRAP_POISON_COLOR,
                                 (poison_icon_x, poison_icon_y),
                                 poison_icon_size)
                pygame.draw.polygon(draw_surface, TRAP_POISON_COLOR, [
                    (poison_icon_x, poison_icon_y - poison_icon_size),
                    (poison_icon_x + poison_icon_size, poison_icon_y + poison_icon_size),
                    (poison_icon_x - poison_icon_size, poison_icon_y + poison_icon_size)
                ])

                # Text s efektem pulzování
                pulse = int(20 * math.sin(current_timestamp * 5)) + 235
                poison_color = (TRAP_POISON_COLOR[0], TRAP_POISON_COLOR[1], TRAP_POISON_COLOR[2], pulse)
                draw_text(draw_surface, poison_text, small_font, poison_color,
                         (SCREEN_WIDTH // 2, effects_text_y), with_bg=False)
                effects_text_y += 30

            if player_stats['slowed']:
                slow_text = f"SLOWED: {int(player_stats['slow_timer'])}s"
                # Ikona zpomalení
                slow_icon_x = effects_bg_x + 30
                slow_icon_y = effects_text_y
                slow_icon_size = 12

                # Modrá sněhová vločka
                for i in range(4):
                    angle = i * math.pi / 4
                    end_x = slow_icon_x + math.cos(angle) * slow_icon_size
                    end_y = slow_icon_y + math.sin(angle) * slow_icon_size
                    pygame.draw.line(draw_surface, TRAP_SLOW_COLOR,
                                   (slow_icon_x, slow_icon_y),
                                   (end_x, end_y), 2)

                # Text s efektem pulzování
                pulse = int(20 * math.sin(current_timestamp * 5)) + 235
                slow_color = (TRAP_SLOW_COLOR[0], TRAP_SLOW_COLOR[1], TRAP_SLOW_COLOR[2], pulse)
                draw_text(draw_surface, slow_text, small_font, slow_color,
                         (SCREEN_WIDTH // 2, effects_text_y), with_bg=False)

        # Informace o obchodu s poloprůhledným pozadím
        shop_text = "P: Shop"
        shop_text_width = small_font.size(shop_text)[0] + 20
        shop_bg = pygame.Surface((shop_text_width, 25), pygame.SRCALPHA)
        shop_bg.fill((0, 0, 0, 120))  # Poloprůhledné černé pozadí
        shop_bg_x = SCREEN_WIDTH - shop_text_width - 10
        shop_bg_y = SCREEN_HEIGHT - 30
        draw_surface.blit(shop_bg, (shop_bg_x, shop_bg_y))

        draw_text(draw_surface, shop_text, small_font, GREY,
                 (SCREEN_WIDTH - 60, SCREEN_HEIGHT - 20), with_bg=False)

        # Zobrazení FPS v pravém horním rohu, pokud je zapnuto
        if show_performance:
            quality_names = ["Low", "Medium", "High"]
            fps_text = f"FPS: {int(perf_monitor.get_fps())}"
            draw_text(draw_surface, fps_text, smaller_font, CYAN, (SCREEN_WIDTH - 50, 50))

        if wave_message_timer > 0:
            draw_text(draw_surface, wave_message_text, wave_font, YELLOW, (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 3))
    # Staré menu bylo nahrazeno novou funkcí draw_main_menu
    elif game_state == STATE_SHOP:
        # Vykreslení pozadí obchodu
        shop_bg = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
        shop_bg.fill((0, 0, 0, 200))
        draw_surface.blit(shop_bg, (0, 0))

        # Nadpis a skóre
        draw_text(draw_surface, "Shop", large_font, YELLOW, (SCREEN_WIDTH//2, 80))
        draw_text(draw_surface, f"Your Score: {score}", default_font, WHITE, (SCREEN_WIDTH//2, 140))

        # Vykreslení položek obchodu
        for i, item in enumerate(shop_items):
            if i < len(shop_item_rects):
                item_rect = shop_item_rects[i]

                # Kontrola, zda je položka zbraň a zda ji již vlastníme
                is_weapon = item['id'] in ['shotgun', 'machinegun']
                is_owned = False

                if is_weapon:
                    if item['id'] == 'shotgun':
                        is_owned = weapons[WEAPON_SHOTGUN]['owned']
                    elif item['id'] == 'machinegun':
                        is_owned = weapons[WEAPON_MACHINEGUN]['owned']

                # Nastavení barev podle dostupnosti
                can_afford = score >= item['cost'] and not is_owned
                disabled = not can_afford or is_owned

                if is_owned:
                    base_color = GREEN  # Již vlastněno
                    text_color_main = BLACK
                    text_color_desc = DARK_GREY
                    border_color = DARK_GREY
                else:
                    base_color = LIGHT_BLUE if can_afford else DARK_GREY
                    text_color_main = BLACK if can_afford else GREY
                    text_color_desc = DARK_GREY if can_afford else GREY
                    border_color = WHITE if can_afford else GREY

                hover_color = WHITE if not is_owned else GREEN

                # Vykreslení tlačítka
                draw_button(draw_surface, "", default_font, BLACK, item_rect,
                           base_color, hover_color, border_color=border_color,
                           disabled=disabled)

                # Vykreslení textu
                text_y_offset = -15
                draw_text(draw_surface, item['name'], small_font, text_color_main,
                         (item_rect.centerx, item_rect.centery + text_y_offset))

                # Popis a cena
                if is_owned:
                    desc_text = f"{item['desc']} | OWNED"
                else:
                    desc_text = f"{item['desc']} | Cost: {item['cost']}"

                draw_text(draw_surface, desc_text, smaller_font, text_color_desc,
                         (item_rect.centerx, item_rect.centery - text_y_offset + 18))

        # Tlačítko pro odchod z obchodu
        exit_button_rect = pygame.Rect(SCREEN_WIDTH//2 - 100, SCREEN_HEIGHT - 80, 200, 50)
        draw_button(draw_surface, "Exit Shop", default_font, BLACK, exit_button_rect, RED, WHITE)
    elif game_state == STATE_GAME_OVER: # logika beze změny
        if score > high_score: high_score = score; save_highscore(high_score)

    # Přenos draw_surface do textury (Vylepšeno pro plynulost)
    texture_data = None

    # S VSYNC nepotřebujeme omezovat FPS manuálně
    try:
        # Použití tobytes místo zastaralého tostring
        texture_data = pygame.image.tobytes(draw_surface, 'RGBA', True)
    except Exception as e:
        print(f"Surface to bytes error: {e}")

    if texture_data:
        try:
            glActiveTexture(GL_TEXTURE0)
            glBindTexture(GL_TEXTURE_2D, game_texture)
            glTexSubImage2D(GL_TEXTURE_2D, 0, 0, 0, SCREEN_WIDTH, SCREEN_HEIGHT, GL_RGBA, GL_UNSIGNED_BYTE, texture_data)
            glBindTexture(GL_TEXTURE_2D, 0)
        except Exception as e:
            print(f"Texture upload error: {e}")
            texture_data = None

    # Finální kreslení game shaderem (beze změny)
    if game_shader_program and texture_data:
        try:
            glUseProgram(game_shader_program); apply_lighting_effect = (game_state == STATE_PLAYING); glUniform1i(loc_game_applyLighting, apply_lighting_effect)
            if apply_lighting_effect:
                # Použití plynulého efektu svíčky místo náhodného blikání
                display_light_radius = max(0.0, player_stats['light_radius'])
                final_display_radius = display_light_radius  # Blikání je nyní řešeno v shaderu

                # Převod souřadnic pro shader
                shader_light_y = SCREEN_HEIGHT - player_stats['y']

                # Nastavení uniformů pro shader
                glUniform2f(loc_game_lightPos, player_stats['x'], shader_light_y)
                glUniform1f(loc_game_lightRadius, final_display_radius)

                # Normalizace barvy světla
                norm_light_color = (LIGHT_COLOR[0]/255.0, LIGHT_COLOR[1]/255.0, LIGHT_COLOR[2]/255.0)
                glUniform3f(loc_game_lightColor, *norm_light_color)
                glUniform3f(loc_game_ambientColor, *AMBIENT_LIGHT)

                # Předání času do shaderu pro animaci plápolání
                glUniform1f(loc_game_time, float(current_timestamp))
            glUniform2f(loc_game_screenResolution, float(SCREEN_WIDTH), float(SCREEN_HEIGHT)); glUniform1i(loc_game_gameTexture, 0); glActiveTexture(GL_TEXTURE0); glBindTexture(GL_TEXTURE_2D, game_texture); glBindVertexArray(VAO); glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, None); glBindVertexArray(0); glBindTexture(GL_TEXTURE_2D, 0); glUseProgram(0)
        except Exception as e: print(f"Game shader draw error: {e}"); glUseProgram(0)

    # Aktualizace performance monitoru
    perf_monitor.update()

    # Omezení FPS a zobrazení
    pygame.display.flip()
    clock.tick(FPS)

# --- Ukončení (beze změny) ---
print("--- Exiting ---")
try:
    print("Releasing OpenGL resources..."); glBindVertexArray(0); glBindBuffer(GL_ARRAY_BUFFER, 0); glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0); glBindTexture(GL_TEXTURE_2D, 0); glUseProgram(0)
    if VAO: glDeleteVertexArrays(1, [VAO]); print("- VAO deleted.")
    if VBO: glDeleteBuffers(1, [VBO]); print("- VBO deleted.")
    if EBO: glDeleteBuffers(1, [EBO]); print("- EBO deleted.")
    if game_texture: glDeleteTextures(1, [game_texture]); print("- Game texture deleted.")
    if game_shader_program: glDeleteProgram(game_shader_program); print("- Game shader deleted.")
    if menu_shader_program: glDeleteProgram(menu_shader_program); print("- Menu shader deleted.")
    print("OpenGL resources released.")
except Exception as e: print(f"Error releasing OpenGL resources: {e}")
pygame.quit(); print("Pygame quit."); sys.exit()